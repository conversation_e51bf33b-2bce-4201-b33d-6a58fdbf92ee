"""
数据库连接和会话管理
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
import redis
from typing import Generator
import logging

from .config import settings, DatabaseConfig, RedisConfig

# 配置日志
logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.database_url_sync,
    poolclass=QueuePool,
    **DatabaseConfig.get_engine_config()
)

# 创建会话工厂
SessionLocal = sessionmaker(
    bind=engine,
    **DatabaseConfig.get_session_config()
)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()

# Redis连接
redis_client = redis.Redis(**RedisConfig.get_connection_config())


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    用于依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"数据库会话错误: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def get_redis() -> redis.Redis:
    """
    获取Redis连接
    """
    return redis_client


class DatabaseManager:
    """数据库管理器"""
    
    @staticmethod
    def create_tables():
        """创建所有表"""
        try:
            Base.metadata.create_all(bind=engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    @staticmethod
    def drop_tables():
        """删除所有表"""
        try:
            Base.metadata.drop_all(bind=engine)
            logger.info("数据库表删除成功")
        except Exception as e:
            logger.error(f"删除数据库表失败: {e}")
            raise
    
    @staticmethod
    def check_connection():
        """检查数据库连接"""
        try:
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            logger.info("数据库连接正常")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    @staticmethod
    def get_db_info():
        """获取数据库信息"""
        try:
            with engine.connect() as conn:
                result = conn.execute("SELECT VERSION() as version")
                version = result.fetchone()[0]
                
                result = conn.execute("SELECT DATABASE() as db_name")
                db_name = result.fetchone()[0]
                
                return {
                    "version": version,
                    "database": db_name,
                    "url": settings.database_url_sync.split("@")[1] if "@" in settings.database_url_sync else "unknown"
                }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return None


class RedisManager:
    """Redis管理器"""
    
    @staticmethod
    def check_connection():
        """检查Redis连接"""
        try:
            redis_client.ping()
            logger.info("Redis连接正常")
            return True
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return False
    
    @staticmethod
    def get_redis_info():
        """获取Redis信息"""
        try:
            info = redis_client.info()
            return {
                "version": info.get("redis_version"),
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "uptime": info.get("uptime_in_seconds")
            }
        except Exception as e:
            logger.error(f"获取Redis信息失败: {e}")
            return None
    
    @staticmethod
    def clear_cache(pattern: str = "*"):
        """清除缓存"""
        try:
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
                logger.info(f"清除缓存成功，删除了 {len(keys)} 个键")
                return len(keys)
            return 0
        except Exception as e:
            logger.error(f"清除缓存失败: {e}")
            return -1


# 缓存键前缀
class CacheKeys:
    """缓存键常量"""
    USER_SESSION = "user_session:"
    FILE_INFO = "file_info:"
    SEARCH_RESULT = "search_result:"
    THUMBNAIL = "thumbnail:"
    DOWNLOAD_COUNT = "download_count:"
    USER_QUOTA = "user_quota:"
    SYSTEM_CONFIG = "system_config:"
    FOLDER_PERMISSIONS = "folder_permissions:"
    
    @staticmethod
    def user_session(user_id: int) -> str:
        return f"{CacheKeys.USER_SESSION}{user_id}"
    
    @staticmethod
    def file_info(file_id: int) -> str:
        return f"{CacheKeys.FILE_INFO}{file_id}"
    
    @staticmethod
    def search_result(query_hash: str) -> str:
        return f"{CacheKeys.SEARCH_RESULT}{query_hash}"
    
    @staticmethod
    def thumbnail(file_id: int) -> str:
        return f"{CacheKeys.THUMBNAIL}{file_id}"
    
    @staticmethod
    def download_count(user_id: int, date: str) -> str:
        return f"{CacheKeys.DOWNLOAD_COUNT}{user_id}:{date}"
    
    @staticmethod
    def user_quota(user_id: int) -> str:
        return f"{CacheKeys.USER_QUOTA}{user_id}"
    
    @staticmethod
    def system_config(key: str) -> str:
        return f"{CacheKeys.SYSTEM_CONFIG}{key}"
    
    @staticmethod
    def folder_permissions(user_id: int, folder_path: str) -> str:
        import hashlib
        path_hash = hashlib.md5(folder_path.encode()).hexdigest()
        return f"{CacheKeys.FOLDER_PERMISSIONS}{user_id}:{path_hash}"


# 初始化函数
def init_database():
    """初始化数据库"""
    logger.info("正在初始化数据库...")
    
    # 检查数据库连接
    if not DatabaseManager.check_connection():
        raise Exception("数据库连接失败")
    
    # 检查Redis连接
    if not RedisManager.check_connection():
        logger.warning("Redis连接失败，某些功能可能不可用")
    
    # 创建表（如果不存在）
    try:
        DatabaseManager.create_tables()
    except Exception as e:
        logger.error(f"初始化数据库表失败: {e}")
        raise
    
    logger.info("数据库初始化完成")


# 导出
__all__ = [
    "engine",
    "SessionLocal", 
    "Base",
    "metadata",
    "redis_client",
    "get_db",
    "get_redis",
    "DatabaseManager",
    "RedisManager",
    "CacheKeys",
    "init_database",
]
