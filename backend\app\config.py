"""
配置管理模块
"""
import os
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = Field(default="企业级文件共享系统", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # 数据库配置
    database_url: str = Field(env="DATABASE_URL")
    database_host: str = Field(default="localhost", env="DATABASE_HOST")
    database_port: int = Field(default=3306, env="DATABASE_PORT")
    database_user: str = Field(default="root", env="DATABASE_USER")
    database_password: str = Field(env="DATABASE_PASSWORD")
    database_name: str = Field(default="fileshare_system", env="DATABASE_NAME")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # JWT配置
    secret_key: str = Field(env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    
    # 文件存储配置
    shared_folders_root: str = Field(default="C:/SharedFiles", env="SHARED_FOLDERS_ROOT")
    thumbnails_path: str = Field(default="./data/thumbnails", env="THUMBNAILS_PATH")
    temp_path: str = Field(default="./data/temp", env="TEMP_PATH")
    logs_path: str = Field(default="./data/logs", env="LOGS_PATH")
    
    # 文件上传限制
    max_file_size: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    max_batch_download_size: int = Field(default=500 * 1024 * 1024, env="MAX_BATCH_DOWNLOAD_SIZE")  # 500MB
    max_daily_download_quota: int = Field(default=1000 * 1024 * 1024, env="MAX_DAILY_DOWNLOAD_QUOTA")  # 1GB
    
    # 搜索引擎配置
    everything_enabled: bool = Field(default=True, env="EVERYTHING_ENABLED")
    image_search_enabled: bool = Field(default=True, env="IMAGE_SEARCH_ENABLED")
    everything_path: str = Field(default="C:/Program Files/Everything/Everything.exe", env="EVERYTHING_PATH")
    
    # 安全配置
    enable_external_access: bool = Field(default=False, env="ENABLE_EXTERNAL_ACCESS")
    allowed_ips: str = Field(default="127.0.0.1,***********/24", env="ALLOWED_IPS")
    max_login_attempts: int = Field(default=5, env="MAX_LOGIN_ATTEMPTS")
    lockout_duration: int = Field(default=300, env="LOCKOUT_DURATION")  # 5分钟
    
    # 加密下载配置
    encryption_threshold: int = Field(default=3, env="ENCRYPTION_THRESHOLD")
    password_request_limit: int = Field(default=5, env="PASSWORD_REQUEST_LIMIT")
    
    # 监控配置
    enable_monitoring: bool = Field(default=True, env="ENABLE_MONITORING")
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    
    # 邮件配置
    smtp_host: Optional[str] = Field(default=None, env="SMTP_HOST")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_user: Optional[str] = Field(default=None, env="SMTP_USER")
    smtp_password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    smtp_from: Optional[str] = Field(default=None, env="SMTP_FROM")
    
    # 系统配置
    timezone: str = Field(default="Asia/Shanghai", env="TIMEZONE")
    language: str = Field(default="zh-CN", env="LANGUAGE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保必要的目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.thumbnails_path,
            self.temp_path,
            self.logs_path,
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @property
    def database_url_sync(self) -> str:
        """同步数据库连接URL"""
        if hasattr(self, '_database_url_sync'):
            return self._database_url_sync
        
        if self.database_url:
            return self.database_url
        
        return f"mysql+pymysql://{self.database_user}:{self.database_password}@{self.database_host}:{self.database_port}/{self.database_name}?charset=utf8mb4"
    
    @property
    def database_url_async(self) -> str:
        """异步数据库连接URL"""
        if hasattr(self, '_database_url_async'):
            return self._database_url_async
        
        # 将同步URL转换为异步URL
        sync_url = self.database_url_sync
        return sync_url.replace("mysql+pymysql://", "mysql+aiomysql://")
    
    @property
    def allowed_ips_list(self) -> List[str]:
        """获取允许的IP列表"""
        if isinstance(self.allowed_ips, str):
            return [ip.strip() for ip in self.allowed_ips.split(",") if ip.strip()]
        return self.allowed_ips if self.allowed_ips else ["127.0.0.1"]

    @property
    def cors_origins(self) -> List[str]:
        """CORS允许的源"""
        if self.debug:
            return ["*"]
        return [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
        ]


# 全局配置实例
settings = Settings()


class DatabaseConfig:
    """数据库配置类"""
    
    @staticmethod
    def get_engine_config():
        """获取数据库引擎配置"""
        return {
            "pool_size": 20,
            "max_overflow": 30,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "pool_pre_ping": True,
            "echo": settings.debug,
        }
    
    @staticmethod
    def get_session_config():
        """获取会话配置"""
        return {
            "autocommit": False,
            "autoflush": False,
            "expire_on_commit": False,
        }


class RedisConfig:
    """Redis配置类"""
    
    @staticmethod
    def get_connection_config():
        """获取Redis连接配置"""
        return {
            "host": settings.redis_host,
            "port": settings.redis_port,
            "db": settings.redis_db,
            "decode_responses": True,
            "socket_timeout": 5,
            "socket_connect_timeout": 5,
            "retry_on_timeout": True,
            "health_check_interval": 30,
        }


class SecurityConfig:
    """安全配置类"""
    
    @staticmethod
    def get_cors_config():
        """获取CORS配置"""
        return {
            "allow_origins": settings.cors_origins,
            "allow_credentials": True,
            "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["*"],
        }
    
    @staticmethod
    def get_rate_limit_config():
        """获取限流配置"""
        return {
            "default": "100/minute",
            "login": "10/minute",
            "download": "50/minute",
            "search": "200/minute",
        }


# 导出配置
__all__ = [
    "settings",
    "Settings",
    "DatabaseConfig", 
    "RedisConfig",
    "SecurityConfig",
]
