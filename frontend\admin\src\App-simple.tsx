import React, { useState } from 'react'
import { Button, Card, Layout, Menu, Typography, Space, Avatar, Dropdown } from 'antd'
import {
  DashboardOutlined,
  UserOutlined,
  FileOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons'

const { Header, Sider, Content } = Layout
const { Title, Text } = Typography

function App() {
  const [collapsed, setCollapsed] = useState(false)
  const [currentPage, setCurrentPage] = useState('dashboard')

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '系统概览',
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: 'files',
      icon: <FileOutlined />,
      label: '文件管理',
    },
  ]

  const userMenu = {
    items: [
      {
        key: 'logout',
        label: '退出登录',
        icon: <LogoutOutlined />,
      },
    ],
  }

  const renderContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div>
            <Title level={2}>系统概览</Title>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
              <Card>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>25</div>
                  <div>总用户数</div>
                </div>
              </Card>
              <Card>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>1,234</div>
                  <div>总文件数</div>
                </div>
              </Card>
              <Card>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>2.3GB</div>
                  <div>今日下载量</div>
                </div>
              </Card>
              <Card>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 24, fontWeight: 'bold', color: '#f5222d' }}>15</div>
                  <div>在线用户</div>
                </div>
              </Card>
            </div>
          </div>
        )
      case 'users':
        return (
          <div>
            <Title level={2}>用户管理</Title>
            <Card>
              <p>用户管理功能正在开发中...</p>
            </Card>
          </div>
        )
      case 'files':
        return (
          <div>
            <Title level={2}>文件管理</Title>
            <Card>
              <p>文件管理功能正在开发中...</p>
            </Card>
          </div>
        )
      default:
        return <div>页面未找到</div>
    }
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed} theme="dark">
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          background: '#002140',
          color: 'white',
          fontSize: 18,
          fontWeight: 'bold'
        }}>
          {collapsed ? '🛡️' : '🛡️ 管理控制台'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[currentPage]}
          items={menuItems}
          onClick={({ key }) => setCurrentPage(key)}
        />
      </Sider>
      <Layout>
        <Header style={{ 
          padding: '0 16px', 
          background: '#fff', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />
          <Dropdown menu={userMenu} placement="bottomRight">
            <Space style={{ cursor: 'pointer' }}>
              <Avatar icon={<UserOutlined />} />
              <span>管理员</span>
            </Space>
          </Dropdown>
        </Header>
        <Content style={{ margin: '24px', padding: '24px', background: '#fff', minHeight: 280 }}>
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  )
}

export default App
