"""
系统模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum
from sqlalchemy.sql import func
import enum

from ..database import Base


class ConfigType(str, enum.Enum):
    """配置类型枚举"""
    STRING = "string"
    INT = "int"
    FLOAT = "float"
    BOOLEAN = "boolean"
    JSON = "json"


class NotificationType(str, enum.Enum):
    """通知类型枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class TargetUsers(str, enum.Enum):
    """目标用户枚举"""
    ALL = "all"
    ADMIN = "admin"
    SPECIFIC = "specific"


class SystemConfig(Base):
    """系统配置模型"""
    __tablename__ = "system_config"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="配置ID")
    config_key = Column(String(100), unique=True, nullable=False, index=True, comment="配置键")
    config_value = Column(Text, comment="配置值")
    config_type = Column(Enum(ConfigType), default=ConfigType.STRING, comment="配置类型")
    description = Column(Text, comment="配置描述")
    is_public = Column(Boolean, default=False, index=True, comment="是否公开(前端可访问)")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<SystemConfig(key='{self.config_key}', value='{self.config_value}')>"
    
    def get_typed_value(self):
        """获取类型化的配置值"""
        if self.config_value is None:
            return None
        
        if self.config_type == ConfigType.INT:
            return int(self.config_value)
        elif self.config_type == ConfigType.FLOAT:
            return float(self.config_value)
        elif self.config_type == ConfigType.BOOLEAN:
            return self.config_value.lower() in ('true', '1', 'yes', 'on')
        elif self.config_type == ConfigType.JSON:
            import json
            return json.loads(self.config_value)
        else:
            return self.config_value
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "config_key": self.config_key,
            "config_value": self.config_value,
            "typed_value": self.get_typed_value(),
            "config_type": self.config_type.value,
            "description": self.description,
            "is_public": self.is_public,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class SystemNotification(Base):
    """系统通知模型"""
    __tablename__ = "system_notifications"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="通知ID")
    title = Column(String(200), nullable=False, comment="通知标题")
    content = Column(Text, nullable=False, comment="通知内容")
    notification_type = Column(Enum(NotificationType), default=NotificationType.INFO, comment="通知类型")
    target_users = Column(Enum(TargetUsers), default=TargetUsers.ALL, comment="目标用户")
    
    # 状态字段
    is_active = Column(Boolean, default=True, index=True, comment="是否激活")
    
    # 时间字段
    start_time = Column(DateTime(timezone=True), server_default=func.now(), comment="开始时间")
    end_time = Column(DateTime(timezone=True), comment="结束时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    def __repr__(self):
        return f"<SystemNotification(id={self.id}, title='{self.title}')>"
    
    @property
    def is_expired(self) -> bool:
        """检查通知是否已过期"""
        if self.end_time is None:
            return False
        from datetime import datetime
        return datetime.utcnow() > self.end_time
    
    @property
    def is_valid(self) -> bool:
        """检查通知是否有效"""
        return self.is_active and not self.is_expired
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "notification_type": self.notification_type.value,
            "target_users": self.target_users.value,
            "is_active": self.is_active,
            "is_expired": self.is_expired,
            "is_valid": self.is_valid,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
