# 数据库配置
DATABASE_URL=mysql+pymysql://root:123456@localhost:3306/fileshare_system
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=123456
DATABASE_NAME=fileshare_system

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 应用配置
APP_NAME=企业级文件共享系统
APP_VERSION=1.0.0
DEBUG=True
HOST=0.0.0.0
PORT=8000

# 文件存储配置
SHARED_FOLDERS_ROOT=C:/SharedFiles
THUMBNAILS_PATH=./data/thumbnails
TEMP_PATH=./data/temp
LOGS_PATH=./data/logs

# 上传限制
MAX_FILE_SIZE=100  # MB
MAX_BATCH_DOWNLOAD_SIZE=500  # MB
MAX_DAILY_DOWNLOAD_QUOTA=1000  # MB

# 搜索引擎配置
EVERYTHING_ENABLED=True
IMAGE_SEARCH_ENABLED=True
EVERYTHING_PATH=C:/Program Files/Everything/Everything.exe

# 安全配置
ENABLE_EXTERNAL_ACCESS=False
ALLOWED_IPS=127.0.0.1,***********/24
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=300  # 秒

# 加密下载配置
ENCRYPTION_THRESHOLD=3  # 下载次数超过此值后加密
PASSWORD_REQUEST_LIMIT=5  # 密码申请次数限制

# 监控配置
ENABLE_MONITORING=True
PROMETHEUS_PORT=9090

# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# 系统配置
TIMEZONE=Asia/Shanghai
LANGUAGE=zh-CN
