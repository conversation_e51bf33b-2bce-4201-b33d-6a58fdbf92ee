"""
企业级文件共享系统 - 主应用入口
"""
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import time
import logging
from pathlib import Path

from app.config import settings, SecurityConfig
from app.database import init_database
from app.core.logging import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

# 模板配置
templates = Jinja2Templates(directory="templates")

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="企业级文件共享系统 - 安全、高效、易用的文件管理解决方案",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    **SecurityConfig.get_cors_config()
)

# 添加受信任主机中间件
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.local"]
    )


# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": time.time(),
            "path": str(request.url.path)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "内部服务器错误" if not settings.debug else str(exc),
            "status_code": 500,
            "timestamp": time.time(),
            "path": str(request.url.path)
        }
    )


# 静态文件服务
if Path("static").exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 缩略图静态文件服务
thumbnails_path = Path(settings.thumbnails_path)
if thumbnails_path.exists():
    app.mount("/thumbnails", StaticFiles(directory=str(thumbnails_path)), name="thumbnails")


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    from app.database import DatabaseManager, RedisManager
    
    db_status = DatabaseManager.check_connection()
    redis_status = RedisManager.check_connection()
    
    return {
        "status": "healthy" if db_status and redis_status else "unhealthy",
        "timestamp": time.time(),
        "version": settings.app_version,
        "database": "connected" if db_status else "disconnected",
        "redis": "connected" if redis_status else "disconnected",
        "debug": settings.debug,
    }


# 系统信息端点
@app.get("/info")
async def system_info():
    """系统信息"""
    from app.database import DatabaseManager, RedisManager
    import psutil
    import platform
    
    # 系统信息
    system_info = {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(),
        "memory_total": psutil.virtual_memory().total,
        "memory_available": psutil.virtual_memory().available,
        "disk_usage": psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent,
    }
    
    # 数据库信息
    db_info = DatabaseManager.get_db_info()
    
    # Redis信息
    redis_info = RedisManager.get_redis_info()
    
    return {
        "app_name": settings.app_name,
        "app_version": settings.app_version,
        "system": system_info,
        "database": db_info,
        "redis": redis_info,
        "timestamp": time.time(),
    }


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用{settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else None,
        "health": "/health",
        "info": "/info",
        "timestamp": time.time(),
    }


# 管理界面路由
@app.get("/admin", response_class=HTMLResponse)
async def admin_dashboard(request: Request):
    """管理员控制台"""
    return templates.TemplateResponse("admin/index.html", {"request": request})


@app.get("/admin/", response_class=HTMLResponse)
async def admin_dashboard_slash(request: Request):
    """管理员控制台（带斜杠）"""
    return templates.TemplateResponse("admin/index.html", {"request": request})


@app.get("/login", response_class=HTMLResponse)
async def admin_login(request: Request):
    """管理员登录页面"""
    return templates.TemplateResponse("login.html", {"request": request})


@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users(request: Request):
    """用户管理页面"""
    return templates.TemplateResponse("admin/users.html", {"request": request})


# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info(f"正在启动 {settings.app_name} v{settings.app_version}")
    
    try:
        # 初始化数据库
        init_database()
        logger.info("数据库初始化完成")
        
        # 其他初始化任务
        logger.info("应用启动完成")
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise


# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("正在关闭应用...")
    
    try:
        # 清理资源
        from app.database import redis_client
        redis_client.close()
        
        logger.info("应用关闭完成")
        
    except Exception as e:
        logger.error(f"应用关闭时出错: {e}")


# 导入路由
def include_routers():
    """包含所有路由"""
    try:
        # 认证路由
        from app.api.auth import router as auth_router
        app.include_router(auth_router, prefix="/api/auth", tags=["认证"])
        
        # 管理员路由
        from app.api.admin import router as admin_router
        app.include_router(admin_router, prefix="/api/admin", tags=["管理员"])
        
        # 文件路由
        from app.api.files import router as files_router
        app.include_router(files_router, prefix="/api/files", tags=["文件"])
        
        # 搜索路由
        from app.api.search import router as search_router
        app.include_router(search_router, prefix="/api/search", tags=["搜索"])
        
        # 用户路由
        from app.api.users import router as users_router
        app.include_router(users_router, prefix="/api/users", tags=["用户"])
        
        logger.info("路由加载完成")
        
    except ImportError as e:
        logger.warning(f"某些路由模块未找到: {e}")


# 加载路由
include_routers()


def main():
    """主函数"""
    logger.info(f"启动服务器: {settings.host}:{settings.port}")
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
        access_log=True,
        server_header=False,
        date_header=False,
    )


if __name__ == "__main__":
    main()
