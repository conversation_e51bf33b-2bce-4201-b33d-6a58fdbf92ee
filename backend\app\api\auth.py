"""
认证API路由
"""
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, <PERSON>
from typing import Optional

from ..database import get_db, get_redis
from ..models.user import User, UserGroup
from ..core.security import PasswordManager, TokenManager, IPValidator
from ..core.logging import SecurityLogger, AccessLogger
from ..config import settings

router = APIRouter()
security = HTTPBearer()


# Pydantic模型
class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=1, max_length=128, description="密码")
    user_type: str = Field(default="user", regex="^(admin|user)$", description="用户类型")
    remember_me: bool = Field(default=False, description="记住我")


class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user: dict = Field(..., description="用户信息")


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")


class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    old_password: str = Field(..., min_length=1, max_length=128, description="旧密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")


class UserProfile(BaseModel):
    """用户资料"""
    id: int
    username: str
    email: Optional[str]
    full_name: Optional[str]
    user_group: str
    is_active: bool
    is_admin: bool
    download_quota: int
    remaining_quota: int
    quota_percentage: float
    last_login: Optional[str]
    created_at: str


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    return request.client.host


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db),
    request: Request = None
) -> User:
    """获取当前用户"""
    token = credentials.credentials
    
    try:
        # 验证令牌
        payload = TokenManager.verify_token(token, "access")
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌无效",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 查询用户
        user = db.query(User).filter(User.id == user_id).first()
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户已被禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if user.is_locked:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户已被锁定",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查IP限制
        if request:
            client_ip = get_client_ip(request)
            if not IPValidator.is_allowed_ip(client_ip):
                SecurityLogger.log_permission_denied(
                    user.id, "API访问", "IP限制", client_ip
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="IP地址不被允许",
                )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    request: Request,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """用户登录"""
    client_ip = get_client_ip(request)
    user_agent = request.headers.get("User-Agent", "")
    
    # 检查IP限制
    if not IPValidator.is_allowed_ip(client_ip):
        SecurityLogger.log_login_attempt(
            login_data.username, client_ip, False, user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="IP地址不被允许"
        )
    
    # 查询用户
    user = db.query(User).filter(User.username == login_data.username).first()
    
    if not user:
        SecurityLogger.log_login_attempt(
            login_data.username, client_ip, False, user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 检查用户类型
    if login_data.user_type == "admin" and not user.is_admin:
        SecurityLogger.log_login_attempt(
            login_data.username, client_ip, False, user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无管理员权限"
        )
    
    # 检查用户状态
    if not user.is_active:
        SecurityLogger.log_login_attempt(
            login_data.username, client_ip, False, user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用"
        )
    
    if user.is_locked:
        SecurityLogger.log_login_attempt(
            login_data.username, client_ip, False, user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"用户已被锁定，请稍后再试"
        )
    
    # 验证密码
    if not PasswordManager.verify_password(login_data.password, user.password_hash):
        # 增加登录尝试次数
        user.increment_login_attempts(
            max_attempts=settings.max_login_attempts,
            lockout_minutes=settings.lockout_duration // 60
        )
        db.commit()
        
        SecurityLogger.log_login_attempt(
            login_data.username, client_ip, False, user_agent
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 登录成功，重置登录尝试次数
    user.reset_login_attempts()
    user.update_last_login()
    db.commit()
    
    # 创建令牌
    token_data = {"sub": str(user.id), "username": user.username, "type": login_data.user_type}
    
    # 根据记住我设置过期时间
    if login_data.remember_me:
        expires_delta = timedelta(days=7)
        expires_in = 7 * 24 * 3600
    else:
        expires_delta = timedelta(minutes=settings.access_token_expire_minutes)
        expires_in = settings.access_token_expire_minutes * 60
    
    access_token = TokenManager.create_access_token(token_data, expires_delta)
    refresh_token = TokenManager.create_refresh_token(token_data)
    
    # 记录登录日志
    SecurityLogger.log_login_attempt(
        login_data.username, client_ip, True, user_agent
    )
    
    return LoginResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=expires_in,
        user=user.to_dict()
    )


@router.post("/refresh", response_model=LoginResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        # 验证刷新令牌
        payload = TokenManager.verify_token(refresh_data.refresh_token, "refresh")
        user_id = payload.get("sub")
        
        # 查询用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 创建新的访问令牌
        token_data = {"sub": str(user.id), "username": user.username}
        access_token = TokenManager.create_access_token(token_data)
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_data.refresh_token,
            expires_in=settings.access_token_expire_minutes * 60,
            user=user.to_dict()
        )
        
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌无效"
        )


@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    request: Request = None
):
    """用户登出"""
    client_ip = get_client_ip(request) if request else "unknown"
    
    # 记录登出日志
    SecurityLogger.log_login_attempt(
        current_user.username, client_ip, True, "logout"
    )
    
    return {"message": "登出成功"}


@router.get("/profile", response_model=UserProfile)
async def get_profile(current_user: User = Depends(get_current_user)):
    """获取用户资料"""
    return UserProfile(**current_user.to_dict())


@router.put("/password")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    request: Request = None
):
    """修改密码"""
    # 验证旧密码
    if not PasswordManager.verify_password(password_data.old_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="旧密码错误"
        )
    
    # 验证新密码强度
    is_valid, message = PasswordManager.validate_password_strength(password_data.new_password)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )
    
    # 更新密码
    current_user.password_hash = PasswordManager.get_password_hash(password_data.new_password)
    db.commit()
    
    # 记录安全日志
    client_ip = get_client_ip(request) if request else "unknown"
    SecurityLogger.log_admin_action(
        current_user.id, "修改密码", f"用户ID: {current_user.id}", client_ip
    )
    
    return {"message": "密码修改成功"}


@router.get("/verify")
async def verify_token(current_user: User = Depends(get_current_user)):
    """验证令牌"""
    return {
        "valid": True,
        "user_id": current_user.id,
        "username": current_user.username,
        "is_admin": current_user.is_admin
    }
