"""
安全相关功能模块
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
import secrets
import hashlib
from ipaddress import ip_address, ip_network
import re

from ..config import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class PasswordManager:
    """密码管理器"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, str]:
        """验证密码强度"""
        if len(password) < 8:
            return False, "密码长度至少8位"
        
        if len(password) > 128:
            return False, "密码长度不能超过128位"
        
        # 检查是否包含数字
        if not re.search(r"\d", password):
            return False, "密码必须包含至少一个数字"
        
        # 检查是否包含字母
        if not re.search(r"[a-zA-Z]", password):
            return False, "密码必须包含至少一个字母"
        
        # 检查是否包含特殊字符
        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
            return False, "密码必须包含至少一个特殊字符"
        
        # 检查常见弱密码
        weak_passwords = [
            "12345678", "password", "admin123", "123456789", "qwerty123",
            "password123", "admin", "root", "user", "guest"
        ]
        
        if password.lower() in weak_passwords:
            return False, "密码过于简单，请使用更复杂的密码"
        
        return True, "密码强度符合要求"
    
    @staticmethod
    def generate_random_password(length: int = 12) -> str:
        """生成随机密码"""
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password


class TokenManager:
    """令牌管理器"""
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(data: dict) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=settings.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> dict:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            
            # 检查令牌类型
            if payload.get("type") != token_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌类型错误",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # 检查过期时间
            exp = payload.get("exp")
            if exp is None or datetime.utcnow() > datetime.fromtimestamp(exp):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌已过期",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            return payload
            
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌无效",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    @staticmethod
    def generate_api_key() -> str:
        """生成API密钥"""
        return secrets.token_urlsafe(32)


class IPValidator:
    """IP地址验证器"""
    
    @staticmethod
    def is_allowed_ip(client_ip: str) -> bool:
        """检查IP是否被允许"""
        if not settings.allowed_ips:
            return True
        
        try:
            client_addr = ip_address(client_ip)
            
            for allowed in settings.allowed_ips:
                if "/" in allowed:
                    # CIDR网络
                    if client_addr in ip_network(allowed, strict=False):
                        return True
                else:
                    # 单个IP
                    if client_addr == ip_address(allowed):
                        return True
            
            return False
            
        except ValueError:
            return False
    
    @staticmethod
    def is_internal_ip(ip: str) -> bool:
        """检查是否为内网IP"""
        try:
            addr = ip_address(ip)
            return addr.is_private or addr.is_loopback
        except ValueError:
            return False


class FileHasher:
    """文件哈希计算器"""
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> str:
        """计算文件哈希值"""
        hash_func = hashlib.new(algorithm)
        
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception as e:
            raise Exception(f"计算文件哈希失败: {e}")
    
    @staticmethod
    def calculate_string_hash(text: str, algorithm: str = "sha256") -> str:
        """计算字符串哈希值"""
        hash_func = hashlib.new(algorithm)
        hash_func.update(text.encode('utf-8'))
        return hash_func.hexdigest()


class EncryptionManager:
    """加密管理器"""
    
    @staticmethod
    def generate_encryption_key() -> str:
        """生成加密密钥"""
        return secrets.token_urlsafe(16)
    
    @staticmethod
    def encrypt_data(data: str, key: str) -> str:
        """加密数据（简单实现，生产环境建议使用更强的加密）"""
        from cryptography.fernet import Fernet
        import base64
        
        # 将密钥转换为Fernet格式
        key_bytes = base64.urlsafe_b64encode(key.encode()[:32].ljust(32, b'0'))
        f = Fernet(key_bytes)
        
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    @staticmethod
    def decrypt_data(encrypted_data: str, key: str) -> str:
        """解密数据"""
        from cryptography.fernet import Fernet
        import base64
        
        try:
            # 将密钥转换为Fernet格式
            key_bytes = base64.urlsafe_b64encode(key.encode()[:32].ljust(32, b'0'))
            f = Fernet(key_bytes)
            
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = f.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            raise Exception(f"解密失败: {e}")


class RateLimiter:
    """限流器"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
    
    def is_allowed(self, key: str, limit: int, window: int) -> bool:
        """检查是否允许请求"""
        try:
            current = self.redis.get(key)
            if current is None:
                self.redis.setex(key, window, 1)
                return True
            
            if int(current) >= limit:
                return False
            
            self.redis.incr(key)
            return True
            
        except Exception:
            # Redis连接失败时允许请求
            return True
    
    def get_remaining(self, key: str, limit: int) -> int:
        """获取剩余请求次数"""
        try:
            current = self.redis.get(key)
            if current is None:
                return limit
            return max(0, limit - int(current))
        except Exception:
            return limit


# 导出
__all__ = [
    "PasswordManager",
    "TokenManager", 
    "IPValidator",
    "FileHasher",
    "EncryptionManager",
    "RateLimiter",
]
