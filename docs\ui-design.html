<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业级文件共享系统 - UI设计文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .section {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e8ecf0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
            font-size: 24px;
        }
        
        .mockup {
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #fafbfc;
            position: relative;
        }
        
        .mockup::before {
            content: attr(data-title);
            position: absolute;
            top: -12px;
            left: 20px;
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            min-height: 600px;
        }
        
        .sidebar {
            background: #2c3e50;
            color: white;
            border-radius: 8px;
            padding: 20px;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 10px;
        }
        
        .sidebar-menu a {
            color: #bdc3c7;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: white;
        }
        
        .main-content {
            background: white;
            border-radius: 8px;
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .file-item {
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .file-icon {
            width: 48px;
            height: 48px;
            background: #667eea;
            border-radius: 8px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .search-options {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .checkbox-group {
            display: flex;
            gap: 15px;
            margin: 10px 0;
        }
        
        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }
        
        .notification-bar {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            animation: slideIn 0.5s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>企业级文件共享系统</h1>
            <p>现代化UI设计 - 安全、高效、易用</p>
        </div>

        <!-- 管理员登录界面 -->
        <div class="section">
            <h2>🔐 管理员登录界面</h2>
            <div class="mockup" data-title="管理员登录页面">
                <div class="login-form">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">🛡️</div>
                        <h3 style="color: #2c3e50;">管理员登录</h3>
                        <p style="color: #7f8c8d; font-size: 14px;">Enterprise File Sharing System</p>
                    </div>
                    
                    <div class="form-group">
                        <label>管理员账号</label>
                        <input type="text" class="form-control" placeholder="请输入管理员账号" value="admin">
                    </div>
                    
                    <div class="form-group">
                        <label>登录密码</label>
                        <input type="password" class="form-control" placeholder="请输入登录密码">
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox"> 记住登录状态
                        </label>
                    </div>
                    
                    <button class="btn btn-primary" style="width: 100%; margin-top: 10px;">登录管理系统</button>
                    
                    <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #7f8c8d;">
                        系统版本 v1.0.0 | 安全登录
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户登录界面 -->
        <div class="section">
            <h2>👤 用户登录界面</h2>
            <div class="mockup" data-title="用户登录页面">
                <div class="login-form">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">📁</div>
                        <h3 style="color: #2c3e50;">用户登录</h3>
                        <p style="color: #7f8c8d; font-size: 14px;">文件共享平台</p>
                    </div>
                    
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" class="form-control" placeholder="请输入用户名">
                    </div>
                    
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" class="form-control" placeholder="请输入密码">
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox"> 记住我
                        </label>
                    </div>
                    
                    <button class="btn btn-primary" style="width: 100%; margin-top: 10px;">登录</button>
                    
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="#" style="color: #667eea; text-decoration: none; font-size: 14px;">忘记密码？</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理员主界面 -->
        <div class="section">
            <h2>🎛️ 管理员主界面</h2>
            <div class="mockup" data-title="管理员控制台">
                <div style="background: #2c3e50; color: white; padding: 15px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 20px;">🛡️</div>
                        <h3 style="margin: 0;">管理员控制台</h3>
                    </div>
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <span style="font-size: 14px;">管理员: admin</span>
                        <button class="btn" style="background: #e74c3c; color: white; padding: 6px 12px; font-size: 12px;">退出</button>
                    </div>
                </div>
                
                <div class="dashboard-grid">
                    <div class="sidebar">
                        <ul class="sidebar-menu">
                            <li><a href="#" class="active">📊 系统概览</a></li>
                            <li><a href="#">👥 用户管理</a></li>
                            <li><a href="#">📁 文件管理</a></li>
                            <li><a href="#">🔐 权限控制</a></li>
                            <li><a href="#">📈 访问统计</a></li>
                            <li><a href="#">🔍 搜索引擎</a></li>
                            <li><a href="#">⚙️ 系统设置</a></li>
                            <li><a href="#">🚨 安全监控</a></li>
                            <li><a href="#">📝 操作日志</a></li>
                            <li><a href="#">🌐 网络设置</a></li>
                        </ul>
                    </div>
                    
                    <div class="main-content">
                        <div class="notification-bar">
                            📢 系统运行正常 | 当前在线用户: 15人 | 今日下载量: 2.3GB
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">1,234</div>
                                <div>总文件数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">56</div>
                                <div>在线用户</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">8.9GB</div>
                                <div>今日流量</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">99.8%</div>
                                <div>系统可用性</div>
                            </div>
                        </div>
                        
                        <h4 style="margin-bottom: 15px;">实时监控</h4>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>文件</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>张三</td>
                                    <td>下载</td>
                                    <td>设计图.psd</td>
                                    <td>2分钟前</td>
                                    <td><span class="badge badge-success">成功</span></td>
                                </tr>
                                <tr>
                                    <td>李四</td>
                                    <td>搜索</td>
                                    <td>"产品图片"</td>
                                    <td>5分钟前</td>
                                    <td><span class="badge badge-success">成功</span></td>
                                </tr>
                                <tr>
                                    <td>王五</td>
                                    <td>上传</td>
                                    <td>新产品.jpg</td>
                                    <td>8分钟前</td>
                                    <td><span class="badge badge-warning">处理中</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户主界面 -->
        <div class="section">
            <h2>📁 用户主界面</h2>
            <div class="mockup" data-title="用户文件浏览界面">
                <div style="background: #3498db; color: white; padding: 15px; border-radius: 8px 8px 0 0; display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 20px;">📁</div>
                        <h3 style="margin: 0;">文件共享平台</h3>
                    </div>
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <span style="font-size: 14px;">欢迎, 张三</span>
                        <button class="btn" style="background: #2980b9; color: white; padding: 6px 12px; font-size: 12px;">退出</button>
                    </div>
                </div>
                
                <div style="padding: 20px;">
                    <div class="notification-bar">
                        🎉 欢迎使用文件共享系统！今日可下载配额: 500MB
                    </div>
                    
                    <div class="search-bar">
                        <input type="text" class="search-input" placeholder="搜索文件名、类型或内容...">
                        <button class="btn btn-primary">🔍 搜索</button>
                        <button class="btn" style="background: #95a5a6; color: white;">🖼️ 识图</button>
                    </div>
                    
                    <div class="search-options">
                        <div class="checkbox-group">
                            <label><input type="checkbox" checked> Everything搜索</label>
                            <label><input type="checkbox"> 图像识别</label>
                            <label><input type="checkbox"> 仅显示图片</label>
                        </div>
                        <select class="form-control" style="width: auto;">
                            <option>全部文件夹</option>
                            <option>设计素材</option>
                            <option>产品图片</option>
                            <option>文档资料</option>
                        </select>
                    </div>
                    
                    <div style="display: flex; justify-content: between; align-items: center; margin: 20px 0;">
                        <div style="display: flex; gap: 10px;">
                            <button class="btn" style="background: #f39c12; color: white;">📋 列表</button>
                            <button class="btn btn-primary">🖼️ 大图标</button>
                            <button class="btn" style="background: #95a5a6; color: white;">📄 详情</button>
                        </div>
                        <div style="font-size: 14px; color: #7f8c8d;">
                            共找到 156 个文件
                        </div>
                    </div>
                    
                    <div class="file-grid">
                        <div class="file-item">
                            <div class="file-icon">🖼️</div>
                            <div style="font-size: 12px; margin-bottom: 5px;">产品A.jpg</div>
                            <div style="font-size: 10px; color: #7f8c8d;">2.3MB</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon">🎨</div>
                            <div style="font-size: 12px; margin-bottom: 5px;">设计稿.psd</div>
                            <div style="font-size: 10px; color: #7f8c8d;">15.6MB</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon">📄</div>
                            <div style="font-size: 12px; margin-bottom: 5px;">说明.pdf</div>
                            <div style="font-size: 10px; color: #7f8c8d;">1.2MB</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon">🖼️</div>
                            <div style="font-size: 12px; margin-bottom: 5px;">banner.png</div>
                            <div style="font-size: 10px; color: #7f8c8d;">856KB</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon">🎨</div>
                            <div style="font-size: 12px; margin-bottom: 5px;">logo.ai</div>
                            <div style="font-size: 10px; color: #7f8c8d;">3.4MB</div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon">🖼️</div>
                            <div style="font-size: 12px; margin-bottom: 5px;">photo.tif</div>
                            <div style="font-size: 10px; color: #7f8c8d;">28.9MB</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; color: #7f8c8d;">
            <p>企业级文件共享系统 UI 设计文档</p>
            <p>现代化、安全、高效的文件管理解决方案</p>
        </div>
    </div>
</body>
</html>
