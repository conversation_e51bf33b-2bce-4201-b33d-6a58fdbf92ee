"""
权限模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..database import Base


class FolderPermission(Base):
    """文件夹权限模型"""
    __tablename__ = "folder_permissions"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="权限ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    folder_path = Column(String(1000), nullable=False, index=True, comment="文件夹路径")
    
    # 权限字段
    can_read = Column(Boolean, default=True, comment="可读权限")
    can_download = Column(Boolean, default=False, comment="可下载权限")
    can_upload = Column(Boolean, default=False, comment="可上传权限")
    can_modify = Column(Boolean, default=False, comment="可修改权限")
    can_delete = Column(Boolean, default=False, comment="可删除权限")
    
    # 网络访问权限
    internal_access = Column(Boolean, default=True, comment="内网访问权限")
    external_access = Column(Boolean, default=False, comment="外网访问权限")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="folder_permissions")
    
    def __repr__(self):
        return f"<FolderPermission(user_id={self.user_id}, folder='{self.folder_path}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "folder_path": self.folder_path,
            "can_read": self.can_read,
            "can_download": self.can_download,
            "can_upload": self.can_upload,
            "can_modify": self.can_modify,
            "can_delete": self.can_delete,
            "internal_access": self.internal_access,
            "external_access": self.external_access,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
