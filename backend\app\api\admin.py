"""
管理员API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from ..database import get_db
from ..models.user import User
from ..api.auth import get_current_admin_user

router = APIRouter()


class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    email: Optional[str]
    full_name: Optional[str]
    user_group: str
    is_active: bool
    is_admin: bool
    download_quota: int
    remaining_quota: int
    created_at: str
    last_login: Optional[str]


@router.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    users = db.query(User).offset(skip).limit(limit).all()
    return [UserResponse(**user.to_dict()) for user in users]


@router.get("/dashboard")
async def get_dashboard_stats(
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """获取管理员仪表板统计数据"""
    
    # 用户统计
    total_users = db.query(User).count()
    active_users = db.query(User).filter(User.is_active == True).count()
    
    return {
        "users": {
            "total": total_users,
            "active": active_users,
            "inactive": total_users - active_users
        },
        "files": {
            "total": 0,  # 待实现
            "images": 0,
            "documents": 0
        },
        "system": {
            "uptime": "运行中",
            "version": "1.0.0",
            "status": "healthy"
        }
    }


@router.get("/system/info")
async def get_system_info(
    current_admin: User = Depends(get_current_admin_user)
):
    """获取系统信息"""
    import psutil
    import platform
    from ..config import settings
    
    return {
        "system": {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "memory_used": psutil.virtual_memory().used,
            "disk_usage": psutil.disk_usage('C:').percent if platform.system() == 'Windows' else psutil.disk_usage('/').percent
        },
        "application": {
            "name": settings.app_name,
            "version": settings.app_version,
            "debug": settings.debug
        }
    }
