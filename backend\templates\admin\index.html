<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 企业级文件共享系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 4px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.2rem;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-online { background: #d4edda; color: #155724; }
        .status-offline { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .alert-custom {
            border-left: 4px solid #dc3545;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check"></i>
                企业级文件共享系统 - 管理控制台
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="showProfile()"><i class="bi bi-person"></i> 个人资料</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()"><i class="bi bi-gear"></i> 系统设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-3">
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" onclick="showDashboard()">
                        <i class="bi bi-speedometer2"></i> 系统概览
                    </a>
                    <a class="nav-link" href="#" onclick="showUsers()">
                        <i class="bi bi-people"></i> 用户管理
                    </a>
                    <a class="nav-link" href="#" onclick="showFiles()">
                        <i class="bi bi-folder"></i> 文件管理
                    </a>
                    <a class="nav-link" href="#" onclick="showPermissions()">
                        <i class="bi bi-shield-lock"></i> 权限管理
                    </a>
                    <a class="nav-link" href="#" onclick="showLogs()">
                        <i class="bi bi-journal-text"></i> 日志管理
                    </a>
                    <a class="nav-link" href="#" onclick="showStats()">
                        <i class="bi bi-graph-up"></i> 统计分析
                    </a>
                    <a class="nav-link" href="#" onclick="showSecurity()">
                        <i class="bi bi-shield-exclamation"></i> 安全监控
                    </a>
                    <a class="nav-link" href="#" onclick="showNotifications()">
                        <i class="bi bi-bell"></i> 通知管理
                    </a>
                    <a class="nav-link" href="#" onclick="showSystem()">
                        <i class="bi bi-cpu"></i> 系统状态
                    </a>
                </nav>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-10 main-content">
                <!-- 系统概览页面 -->
                <div id="dashboard-page" class="page-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-speedometer2"></i> 系统概览</h2>
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新数据
                        </button>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-primary" id="total-users">-</div>
                                <div class="text-muted">总用户数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-success" id="total-files">-</div>
                                <div class="text-muted">总文件数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-warning" id="today-downloads">-</div>
                                <div class="text-muted">今日下载</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-info" id="online-users">-</div>
                                <div class="text-muted">在线用户</div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统状态 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-cpu"></i> 系统性能</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>CPU使用率</span>
                                            <span id="cpu-usage">-</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar" id="cpu-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>内存使用率</span>
                                            <span id="memory-usage">-</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" id="memory-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>磁盘使用率</span>
                                            <span id="disk-usage">-</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-warning" id="disk-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-activity"></i> 实时活动</h5>
                                </div>
                                <div class="card-body">
                                    <div id="activity-log" style="height: 200px; overflow-y: auto;">
                                        <div class="loading">
                                            <i class="bi bi-arrow-clockwise spin"></i> 加载中...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clock-history"></i> 最近活动</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>用户</th>
                                            <th>操作</th>
                                            <th>文件</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-activities">
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">
                                                <i class="bi bi-arrow-clockwise spin"></i> 加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他页面内容将通过JavaScript动态加载 -->
                <div id="dynamic-content" class="page-content" style="display: none;">
                    <div class="loading">
                        <i class="bi bi-arrow-clockwise spin"></i> 加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // API基础URL
        const API_BASE = '/api';
        let currentPage = 'dashboard';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            setInterval(loadDashboardData, 30000); // 每30秒刷新一次
        });

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                // 加载统计数据
                const dashboardResponse = await fetch(`${API_BASE}/demo/admin/dashboard`);
                const dashboardData = await dashboardResponse.json();
                
                // 更新统计卡片
                document.getElementById('total-users').textContent = dashboardData.users?.total || 0;
                document.getElementById('total-files').textContent = dashboardData.files?.total || 0;
                document.getElementById('today-downloads').textContent = dashboardData.activity?.today_downloads || 0;
                document.getElementById('online-users').textContent = dashboardData.activity?.online_users || 0;

                // 更新系统性能
                if (dashboardData.system) {
                    updateSystemPerformance(dashboardData.system);
                }

                // 加载最近活动
                loadRecentActivities();

            } catch (error) {
                console.error('加载仪表板数据失败:', error);
            }
        }

        // 更新系统性能显示
        function updateSystemPerformance(systemData) {
            const cpuUsage = systemData.cpu_percent || 0;
            const memoryUsage = systemData.memory_percent || 0;
            const diskUsage = systemData.disk_percent || 0;

            document.getElementById('cpu-usage').textContent = `${cpuUsage.toFixed(1)}%`;
            document.getElementById('cpu-progress').style.width = `${cpuUsage}%`;

            document.getElementById('memory-usage').textContent = `${memoryUsage.toFixed(1)}%`;
            document.getElementById('memory-progress').style.width = `${memoryUsage}%`;

            document.getElementById('disk-usage').textContent = `${diskUsage.toFixed(1)}%`;
            document.getElementById('disk-progress').style.width = `${diskUsage}%`;
        }

        // 加载最近活动
        async function loadRecentActivities() {
            try {
                // 这里使用模拟数据，实际应该调用API
                const activities = [
                    {
                        time: '2024-01-15 14:30:25',
                        user: 'demo_user',
                        action: '下载文件',
                        file: 'document.pdf',
                        status: 'success'
                    },
                    {
                        time: '2024-01-15 14:28:15',
                        user: 'test_user',
                        action: '搜索文件',
                        file: 'image.jpg',
                        status: 'success'
                    },
                    {
                        time: '2024-01-15 14:25:10',
                        user: 'admin',
                        action: '创建用户',
                        file: 'new_user',
                        status: 'success'
                    }
                ];

                const tbody = document.getElementById('recent-activities');
                tbody.innerHTML = activities.map(activity => `
                    <tr>
                        <td>${activity.time}</td>
                        <td>${activity.user}</td>
                        <td>${activity.action}</td>
                        <td>${activity.file}</td>
                        <td>
                            <span class="status-badge ${activity.status === 'success' ? 'status-online' : 'status-offline'}">
                                ${activity.status === 'success' ? '成功' : '失败'}
                            </span>
                        </td>
                    </tr>
                `).join('');

            } catch (error) {
                console.error('加载最近活动失败:', error);
            }
        }

        // 导航函数
        function showDashboard() {
            setActiveNav('dashboard');
            document.getElementById('dashboard-page').style.display = 'block';
            document.getElementById('dynamic-content').style.display = 'none';
            loadDashboardData();
        }

        function showUsers() {
            setActiveNav('users');
            loadPage('users', '用户管理');
        }

        function showFiles() {
            setActiveNav('files');
            loadPage('files', '文件管理');
        }

        function showPermissions() {
            setActiveNav('permissions');
            loadPage('permissions', '权限管理');
        }

        function showLogs() {
            setActiveNav('logs');
            loadPage('logs', '日志管理');
        }

        function showStats() {
            setActiveNav('stats');
            loadPage('stats', '统计分析');
        }

        function showSecurity() {
            setActiveNav('security');
            loadPage('security', '安全监控');
        }

        function showNotifications() {
            setActiveNav('notifications');
            loadPage('notifications', '通知管理');
        }

        function showSystem() {
            setActiveNav('system');
            loadPage('system', '系统状态');
        }

        function showProfile() {
            loadPage('profile', '个人资料');
        }

        function showSettings() {
            loadPage('settings', '系统设置');
        }

        // 设置活动导航
        function setActiveNav(page) {
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            if (page === 'dashboard') {
                document.querySelector('.sidebar .nav-link').classList.add('active');
            }
        }

        // 加载页面内容
        function loadPage(page, title) {
            document.getElementById('dashboard-page').style.display = 'none';
            const dynamicContent = document.getElementById('dynamic-content');
            dynamicContent.style.display = 'block';
            
            // 显示加载中
            dynamicContent.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-${getPageIcon(page)}"></i> ${title}</h2>
                </div>
                <div class="card">
                    <div class="card-body text-center">
                        <div class="loading">
                            <i class="bi bi-arrow-clockwise spin"></i> 正在加载${title}...
                        </div>
                        <p class="text-muted mt-3">该功能正在开发中，敬请期待！</p>
                        <button class="btn btn-primary" onclick="showDashboard()">
                            <i class="bi bi-arrow-left"></i> 返回概览
                        </button>
                    </div>
                </div>
            `;
        }

        // 获取页面图标
        function getPageIcon(page) {
            const icons = {
                'users': 'people',
                'files': 'folder',
                'permissions': 'shield-lock',
                'logs': 'journal-text',
                'stats': 'graph-up',
                'security': 'shield-exclamation',
                'notifications': 'bell',
                'system': 'cpu',
                'profile': 'person',
                'settings': 'gear'
            };
            return icons[page] || 'circle';
        }

        // 刷新仪表板
        function refreshDashboard() {
            loadDashboardData();
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                window.location.href = '/';
            }
        }

        // CSS动画
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
