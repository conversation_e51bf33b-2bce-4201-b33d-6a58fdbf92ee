"""
日志模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, BigInteger, Text, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum

from ..database import Base


class ActionType(str, enum.Enum):
    """操作类型枚举"""
    LOGIN = "login"
    LOGOUT = "logout"
    SEARCH = "search"
    DOWNLOAD = "download"
    UPLOAD = "upload"
    VIEW = "view"
    DELETE = "delete"
    MODIFY = "modify"


class SearchType(str, enum.Enum):
    """搜索类型枚举"""
    FILENAME = "filename"
    IMAGE = "image"
    CONTENT = "content"


class DownloadType(str, enum.Enum):
    """下载类型枚举"""
    SINGLE = "single"
    BATCH = "batch"
    FOLDER = "folder"


class UserLog(Base):
    """用户操作日志模型"""
    __tablename__ = "user_logs"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="日志ID")
    user_id = Column(Integer, ForeignKey("users.id"), index=True, comment="用户ID")
    username = Column(String(50), comment="用户名(冗余字段)")
    action = Column(Enum(ActionType), nullable=False, index=True, comment="操作类型")
    
    # 目标信息
    target_file = Column(String(1000), comment="目标文件")
    target_folder = Column(String(1000), comment="目标文件夹")
    search_query = Column(String(500), comment="搜索关键词")
    
    # 请求信息
    ip_address = Column(String(45), index=True, comment="IP地址")
    user_agent = Column(Text, comment="用户代理")
    
    # 结果信息
    success = Column(Boolean, default=True, index=True, comment="操作是否成功")
    error_message = Column(Text, comment="错误信息")
    file_size = Column(BigInteger, comment="文件大小(用于下载统计)")
    duration_ms = Column(Integer, comment="操作耗时(毫秒)")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True, comment="创建时间")
    
    # 关系
    user = relationship("User", back_populates="user_logs")
    
    def __repr__(self):
        return f"<UserLog(id={self.id}, user='{self.username}', action='{self.action}')>"


class DownloadRecord(Base):
    """下载记录模型"""
    __tablename__ = "download_records"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="记录ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    file_id = Column(Integer, ForeignKey("files.id"), nullable=False, index=True, comment="文件ID")
    file_path = Column(String(1000), nullable=False, comment="文件路径")
    download_size = Column(BigInteger, nullable=False, comment="下载大小(字节)")
    
    # 加密信息
    is_encrypted = Column(Boolean, default=False, index=True, comment="是否加密")
    encryption_password = Column(String(100), comment="加密密码")
    password_requested = Column(Boolean, default=False, index=True, comment="是否申请密码")
    password_approved = Column(Boolean, default=False, comment="密码是否批准")
    password_request_count = Column(Integer, default=0, comment="密码申请次数")
    
    # 下载信息
    download_type = Column(Enum(DownloadType), default=DownloadType.SINGLE, comment="下载类型")
    ip_address = Column(String(45), comment="IP地址")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True, comment="创建时间")
    
    # 关系
    user = relationship("User", back_populates="download_records")
    file = relationship("File", back_populates="download_records")
    
    def __repr__(self):
        return f"<DownloadRecord(id={self.id}, user_id={self.user_id}, file_id={self.file_id})>"


class SearchHistory(Base):
    """搜索历史模型"""
    __tablename__ = "search_history"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="历史ID")
    user_id = Column(Integer, ForeignKey("users.id"), index=True, comment="用户ID")
    search_query = Column(String(500), nullable=False, index=True, comment="搜索关键词")
    search_type = Column(Enum(SearchType), default=SearchType.FILENAME, index=True, comment="搜索类型")
    
    # 结果信息
    result_count = Column(Integer, default=0, comment="结果数量")
    search_time_ms = Column(Integer, comment="搜索耗时(毫秒)")
    
    # 请求信息
    ip_address = Column(String(45), comment="IP地址")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True, comment="创建时间")
    
    # 关系
    user = relationship("User", back_populates="search_history")
    
    def __repr__(self):
        return f"<SearchHistory(id={self.id}, query='{self.search_query}', type='{self.search_type}')>"
