"""
用户模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Date
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime, date
import enum

from ..database import Base


class UserGroup(str, enum.Enum):
    """用户组枚举"""
    READONLY = "readonly"      # 只读
    DOWNLOAD = "download"      # 下载
    UPLOAD = "upload"         # 上传
    MODIFY = "modify"         # 修改
    ADMIN = "admin"           # 管理员


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="用户ID")
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    email = Column(String(100), comment="邮箱")
    full_name = Column(String(100), comment="真实姓名")
    
    # 权限字段
    user_group = Column(Enum(UserGroup), default=UserGroup.READONLY, comment="用户组")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_admin = Column(Boolean, default=False, comment="是否管理员")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_login = Column(DateTime(timezone=True), comment="最后登录时间")
    
    # 安全字段
    login_attempts = Column(Integer, default=0, comment="登录尝试次数")
    locked_until = Column(DateTime(timezone=True), comment="锁定到期时间")
    
    # 配额字段
    download_quota = Column(Integer, default=500, comment="每日下载配额(MB)")
    daily_download_count = Column(Integer, default=0, comment="今日下载量(MB)")
    download_reset_date = Column(Date, default=date.today, comment="下载量重置日期")
    
    # 关系
    folder_permissions = relationship("FolderPermission", back_populates="user", cascade="all, delete-orphan")
    user_logs = relationship("UserLog", back_populates="user")
    download_records = relationship("DownloadRecord", back_populates="user")
    search_history = relationship("SearchHistory", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', group='{self.user_group}')>"
    
    @property
    def is_locked(self) -> bool:
        """检查用户是否被锁定"""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    @property
    def remaining_quota(self) -> int:
        """剩余下载配额(MB)"""
        # 检查是否需要重置每日下载量
        today = date.today()
        if self.download_reset_date != today:
            return self.download_quota
        
        return max(0, self.download_quota - self.daily_download_count)
    
    @property
    def quota_percentage(self) -> float:
        """配额使用百分比"""
        if self.download_quota == 0:
            return 0.0
        return min(100.0, (self.daily_download_count / self.download_quota) * 100)
    
    def can_download(self, size_mb: int) -> bool:
        """检查是否可以下载指定大小的文件"""
        if not self.is_active or self.is_locked:
            return False
        
        if self.user_group in [UserGroup.READONLY]:
            return False
        
        return self.remaining_quota >= size_mb
    
    def can_upload(self) -> bool:
        """检查是否可以上传文件"""
        if not self.is_active or self.is_locked:
            return False
        
        return self.user_group in [UserGroup.UPLOAD, UserGroup.MODIFY, UserGroup.ADMIN]
    
    def can_modify(self) -> bool:
        """检查是否可以修改文件"""
        if not self.is_active or self.is_locked:
            return False
        
        return self.user_group in [UserGroup.MODIFY, UserGroup.ADMIN]
    
    def can_delete(self) -> bool:
        """检查是否可以删除文件"""
        if not self.is_active or self.is_locked:
            return False
        
        return self.user_group in [UserGroup.ADMIN]
    
    def reset_daily_quota(self):
        """重置每日配额"""
        today = date.today()
        if self.download_reset_date != today:
            self.daily_download_count = 0
            self.download_reset_date = today
    
    def add_download_usage(self, size_mb: int):
        """增加下载使用量"""
        self.reset_daily_quota()
        self.daily_download_count += size_mb
    
    def reset_login_attempts(self):
        """重置登录尝试次数"""
        self.login_attempts = 0
        self.locked_until = None
    
    def increment_login_attempts(self, max_attempts: int = 5, lockout_minutes: int = 5):
        """增加登录尝试次数"""
        self.login_attempts += 1
        if self.login_attempts >= max_attempts:
            from datetime import timedelta
            self.locked_until = datetime.utcnow() + timedelta(minutes=lockout_minutes)
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()
    
    def to_dict(self, include_sensitive: bool = False):
        """转换为字典"""
        data = {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "user_group": self.user_group.value,
            "is_active": self.is_active,
            "is_admin": self.is_admin,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "download_quota": self.download_quota,
            "daily_download_count": self.daily_download_count,
            "remaining_quota": self.remaining_quota,
            "quota_percentage": self.quota_percentage,
            "is_locked": self.is_locked,
        }
        
        if include_sensitive:
            data.update({
                "login_attempts": self.login_attempts,
                "locked_until": self.locked_until.isoformat() if self.locked_until else None,
                "download_reset_date": self.download_reset_date.isoformat() if self.download_reset_date else None,
            })
        
        return data
    
    @classmethod
    def create_admin(cls, username: str, password_hash: str, email: str = None, full_name: str = None):
        """创建管理员用户"""
        return cls(
            username=username,
            password_hash=password_hash,
            email=email,
            full_name=full_name or "系统管理员",
            user_group=UserGroup.ADMIN,
            is_admin=True,
            is_active=True,
            download_quota=9999,  # 管理员不限制下载配额
        )
    
    @classmethod
    def create_user(cls, username: str, password_hash: str, user_group: UserGroup = UserGroup.READONLY, 
                   email: str = None, full_name: str = None, download_quota: int = 500):
        """创建普通用户"""
        return cls(
            username=username,
            password_hash=password_hash,
            email=email,
            full_name=full_name,
            user_group=user_group,
            is_admin=False,
            is_active=True,
            download_quota=download_quota,
        )
