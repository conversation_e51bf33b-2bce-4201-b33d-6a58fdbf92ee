<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面 - 企业级文件共享系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        .btn-test {
            margin: 5px;
            min-width: 120px;
        }
        .result-box {
            background: #f1f3f4;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="bi bi-gear"></i> 系统功能测试</h1>
            <p class="text-muted">企业级文件共享系统 - 后端功能验证</p>
        </div>

        <!-- 基础API测试 -->
        <div class="test-section">
            <h3><i class="bi bi-heart-pulse"></i> 基础API测试</h3>
            <button class="btn btn-primary btn-test" onclick="testHealth()">健康检查</button>
            <button class="btn btn-info btn-test" onclick="testInfo()">系统信息</button>
            <button class="btn btn-secondary btn-test" onclick="testRoot()">根路径</button>
            <div id="basic-result" class="result-box" style="display: none;"></div>
        </div>

        <!-- 认证API测试 -->
        <div class="test-section">
            <h3><i class="bi bi-shield-check"></i> 认证API测试</h3>
            <button class="btn btn-success btn-test" onclick="testLogin()">登录测试</button>
            <button class="btn btn-warning btn-test" onclick="testVerify()">令牌验证</button>
            <div id="auth-result" class="result-box" style="display: none;"></div>
        </div>

        <!-- 演示API测试 -->
        <div class="test-section">
            <h3><i class="bi bi-play-circle"></i> 演示API测试</h3>
            <button class="btn btn-outline-primary btn-test" onclick="testDemoLogin()">演示登录</button>
            <button class="btn btn-outline-success btn-test" onclick="testDemoFiles()">演示文件</button>
            <button class="btn btn-outline-info btn-test" onclick="testDemoSearch()">演示搜索</button>
            <button class="btn btn-outline-warning btn-test" onclick="testDemoDashboard()">演示仪表板</button>
            <div id="demo-result" class="result-box" style="display: none;"></div>
        </div>

        <!-- 界面链接测试 -->
        <div class="test-section">
            <h3><i class="bi bi-window"></i> 界面链接测试</h3>
            <a href="/login" class="btn btn-primary btn-test" target="_blank">登录页面</a>
            <a href="/admin" class="btn btn-success btn-test" target="_blank">管理控制台</a>
            <a href="/admin/users" class="btn btn-info btn-test" target="_blank">用户管理</a>
            <a href="/docs" class="btn btn-warning btn-test" target="_blank">API文档</a>
        </div>

        <!-- 系统状态 -->
        <div class="test-section">
            <h3><i class="bi bi-activity"></i> 系统状态</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5>服务器状态</h5>
                            <span id="server-status" class="badge bg-secondary">检查中...</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5>API响应时间</h5>
                            <span id="response-time" class="badge bg-secondary">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 演示账户信息 -->
        <div class="test-section">
            <h3><i class="bi bi-person-badge"></i> 演示账户</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>管理员</h6>
                            <p><strong>用户名:</strong> admin</p>
                            <p><strong>密码:</strong> admin123</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>演示用户</h6>
                            <p><strong>用户名:</strong> demo_user</p>
                            <p><strong>密码:</strong> demo123</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h6>测试用户</h6>
                            <p><strong>用户名:</strong> test_user</p>
                            <p><strong>密码:</strong> test123</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面加载完成后检查服务器状态
        document.addEventListener('DOMContentLoaded', function() {
            checkServerStatus();
        });

        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const startTime = Date.now();
                const response = await fetch('/health');
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    document.getElementById('server-status').textContent = '正常运行';
                    document.getElementById('server-status').className = 'badge bg-success';
                } else {
                    document.getElementById('server-status').textContent = '异常';
                    document.getElementById('server-status').className = 'badge bg-danger';
                }
                
                document.getElementById('response-time').textContent = `${responseTime}ms`;
                document.getElementById('response-time').className = responseTime < 100 ? 'badge bg-success' : 
                                                                      responseTime < 500 ? 'badge bg-warning' : 'badge bg-danger';
            } catch (error) {
                document.getElementById('server-status').textContent = '离线';
                document.getElementById('server-status').className = 'badge bg-danger';
                document.getElementById('response-time').textContent = '超时';
                document.getElementById('response-time').className = 'badge bg-danger';
            }
        }

        // 基础API测试
        async function testHealth() {
            await testAPI('/health', 'basic-result');
        }

        async function testInfo() {
            await testAPI('/info', 'basic-result');
        }

        async function testRoot() {
            await testAPI('/', 'basic-result');
        }

        // 认证API测试
        async function testLogin() {
            const loginData = {
                username: 'admin',
                password: 'admin123',
                user_type: 'admin'
            };
            
            await testAPI('/api/auth/login', 'auth-result', 'POST', loginData);
        }

        async function testVerify() {
            await testAPI('/api/auth/verify', 'auth-result');
        }

        // 演示API测试
        async function testDemoLogin() {
            await testAPI('/api/demo/login', 'demo-result');
        }

        async function testDemoFiles() {
            await testAPI('/api/demo/files', 'demo-result');
        }

        async function testDemoSearch() {
            await testAPI('/api/demo/search?q=test', 'demo-result');
        }

        async function testDemoDashboard() {
            await testAPI('/api/demo/admin/dashboard', 'demo-result');
        }

        // 通用API测试函数
        async function testAPI(url, resultId, method = 'GET', data = null) {
            const resultBox = document.getElementById(resultId);
            resultBox.style.display = 'block';
            resultBox.innerHTML = '<span class="status-info">正在测试...</span>';
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const startTime = Date.now();
                const response = await fetch(url, options);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                const result = await response.json();
                
                let output = `<span class="status-info">请求: ${method} ${url}</span>\n`;
                output += `<span class="status-info">响应时间: ${responseTime}ms</span>\n`;
                output += `<span class="status-${response.ok ? 'success' : 'error'}">状态码: ${response.status}</span>\n`;
                output += `<span class="status-info">响应数据:</span>\n`;
                output += JSON.stringify(result, null, 2);
                
                resultBox.innerHTML = output;
                
            } catch (error) {
                resultBox.innerHTML = `<span class="status-error">错误: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
