"""
日志配置模块
"""
import logging
import logging.handlers
import sys
from pathlib import Path
from loguru import logger
import json
from datetime import datetime

from ..config import settings


class InterceptHandler(logging.Handler):
    """拦截标准库日志并重定向到loguru"""
    
    def emit(self, record):
        # 获取对应的loguru级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # 查找调用者
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_logging():
    """设置日志配置"""
    
    # 移除默认处理器
    logger.remove()
    
    # 控制台输出格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件输出格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level="DEBUG" if settings.debug else "INFO",
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # 确保日志目录存在
    log_dir = Path(settings.logs_path)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 应用日志文件
    logger.add(
        log_dir / "app.log",
        format=file_format,
        level="INFO",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True,
    )
    
    # 错误日志文件
    logger.add(
        log_dir / "error.log",
        format=file_format,
        level="ERROR",
        rotation="10 MB", 
        retention="90 days",
        compression="zip",
        encoding="utf-8",
        backtrace=True,
        diagnose=True,
    )
    
    # 访问日志文件
    logger.add(
        log_dir / "access.log",
        format=file_format,
        level="INFO",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "access" in record["extra"],
    )
    
    # 安全日志文件
    logger.add(
        log_dir / "security.log",
        format=file_format,
        level="WARNING",
        rotation="10 MB",
        retention="180 days",
        compression="zip",
        encoding="utf-8",
        filter=lambda record: "security" in record["extra"],
    )
    
    # 拦截标准库日志
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    
    logger.info("日志系统初始化完成")


class SecurityLogger:
    """安全日志记录器"""
    
    @staticmethod
    def log_login_attempt(username: str, ip: str, success: bool, user_agent: str = None):
        """记录登录尝试"""
        logger.bind(security=True).info(
            f"登录尝试 - 用户: {username}, IP: {ip}, 成功: {success}, UA: {user_agent}"
        )
    
    @staticmethod
    def log_permission_denied(user_id: int, resource: str, action: str, ip: str):
        """记录权限拒绝"""
        logger.bind(security=True).warning(
            f"权限拒绝 - 用户ID: {user_id}, 资源: {resource}, 操作: {action}, IP: {ip}"
        )
    
    @staticmethod
    def log_suspicious_activity(user_id: int, activity: str, details: dict, ip: str):
        """记录可疑活动"""
        logger.bind(security=True).warning(
            f"可疑活动 - 用户ID: {user_id}, 活动: {activity}, 详情: {json.dumps(details)}, IP: {ip}"
        )
    
    @staticmethod
    def log_file_access(user_id: int, file_path: str, action: str, ip: str):
        """记录文件访问"""
        logger.bind(security=True).info(
            f"文件访问 - 用户ID: {user_id}, 文件: {file_path}, 操作: {action}, IP: {ip}"
        )
    
    @staticmethod
    def log_admin_action(admin_id: int, action: str, target: str, ip: str):
        """记录管理员操作"""
        logger.bind(security=True).info(
            f"管理员操作 - 管理员ID: {admin_id}, 操作: {action}, 目标: {target}, IP: {ip}"
        )


class AccessLogger:
    """访问日志记录器"""
    
    @staticmethod
    def log_request(method: str, path: str, status_code: int, response_time: float, 
                   ip: str, user_agent: str = None, user_id: int = None):
        """记录请求"""
        logger.bind(access=True).info(
            f"{method} {path} {status_code} {response_time:.3f}s - "
            f"IP: {ip}, 用户ID: {user_id}, UA: {user_agent}"
        )
    
    @staticmethod
    def log_download(user_id: int, file_path: str, file_size: int, ip: str):
        """记录下载"""
        logger.bind(access=True).info(
            f"下载 - 用户ID: {user_id}, 文件: {file_path}, 大小: {file_size}, IP: {ip}"
        )
    
    @staticmethod
    def log_upload(user_id: int, file_path: str, file_size: int, ip: str):
        """记录上传"""
        logger.bind(access=True).info(
            f"上传 - 用户ID: {user_id}, 文件: {file_path}, 大小: {file_size}, IP: {ip}"
        )
    
    @staticmethod
    def log_search(user_id: int, query: str, result_count: int, search_time: float, ip: str):
        """记录搜索"""
        logger.bind(access=True).info(
            f"搜索 - 用户ID: {user_id}, 查询: {query}, 结果数: {result_count}, "
            f"耗时: {search_time:.3f}s, IP: {ip}"
        )


class PerformanceLogger:
    """性能日志记录器"""
    
    @staticmethod
    def log_slow_query(query: str, duration: float, params: dict = None):
        """记录慢查询"""
        logger.warning(
            f"慢查询 - 耗时: {duration:.3f}s, SQL: {query}, 参数: {params}"
        )
    
    @staticmethod
    def log_cache_miss(cache_key: str, operation: str):
        """记录缓存未命中"""
        logger.info(f"缓存未命中 - 键: {cache_key}, 操作: {operation}")
    
    @staticmethod
    def log_resource_usage(cpu_percent: float, memory_percent: float, disk_percent: float):
        """记录资源使用情况"""
        logger.info(
            f"资源使用 - CPU: {cpu_percent:.1f}%, 内存: {memory_percent:.1f}%, "
            f"磁盘: {disk_percent:.1f}%"
        )


# 导出
__all__ = [
    "setup_logging",
    "SecurityLogger",
    "AccessLogger", 
    "PerformanceLogger",
]
