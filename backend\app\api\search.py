"""
搜索API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from ..database import get_db
from ..models.user import User
from ..api.auth import get_current_user

router = APIRouter()


class SearchResult(BaseModel):
    """搜索结果模型"""
    id: int
    file_name: str
    file_path: str
    file_size: int
    file_type: Optional[str]
    is_image: bool
    score: float


@router.get("/files")
async def search_files(
    q: str = Query(..., description="搜索关键词"),
    engine: str = Query("filename", description="搜索引擎类型"),
    limit: int = Query(50, description="结果数量限制"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """搜索文件"""
    
    if not q.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="搜索关键词不能为空"
        )
    
    # 示例搜索结果
    sample_results = [
        {
            "id": 1,
            "file_name": f"包含'{q}'的文件1.jpg",
            "file_path": "/shared/images/file1.jpg",
            "file_size": 2048000,
            "file_type": "image/jpeg",
            "is_image": True,
            "score": 0.95
        },
        {
            "id": 2,
            "file_name": f"相关文档_{q}.pdf",
            "file_path": "/shared/docs/doc1.pdf",
            "file_size": 1024000,
            "file_type": "application/pdf",
            "is_image": False,
            "score": 0.85
        }
    ]
    
    return {
        "query": q,
        "engine": engine,
        "results": sample_results[:limit],
        "total": len(sample_results),
        "search_time": 0.123
    }


@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., description="搜索关键词"),
    current_user: User = Depends(get_current_user)
):
    """获取搜索建议"""
    
    suggestions = [
        f"{q}相关文件",
        f"{q}图片",
        f"{q}文档",
        f"最新{q}"
    ]
    
    return {
        "query": q,
        "suggestions": suggestions
    }
