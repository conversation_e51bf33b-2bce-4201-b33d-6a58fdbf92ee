"""
搜索API路由 - 完整功能实现
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import os
import re
import time
from pathlib import Path

from ..database import get_db
from ..models.user import User
from ..models.file import File
from ..models.log import SearchHistory, SearchType
from ..api.auth import get_current_user
from ..core.logging import AccessLogger
from ..config import settings

router = APIRouter()


# ==================== 数据模型 ====================

class SearchResult(BaseModel):
    """搜索结果模型"""
    id: int
    file_name: str
    file_path: str
    file_size: int
    size_mb: float
    file_type: Optional[str]
    mime_type: Optional[str]
    is_image: bool
    is_sensitive: bool
    folder_path: str
    extension: str
    created_at: str
    last_accessed: Optional[str]
    access_count: int
    score: float
    highlight: Optional[str]


class SearchSuggestion(BaseModel):
    """搜索建议模型"""
    text: str
    type: str
    count: int


class SearchResponse(BaseModel):
    """搜索响应模型"""
    query: str
    engine: str
    results: List[SearchResult]
    total: int
    search_time: float
    suggestions: List[str]
    filters: Dict[str, Any]


# ==================== 搜索引擎 ====================

class FileSearchEngine:
    """文件搜索引擎"""

    @staticmethod
    async def search_by_filename(
        query: str,
        db: Session,
        user_id: int,
        limit: int = 50,
        file_type: Optional[str] = None,
        folder_path: Optional[str] = None
    ) -> List[Dict]:
        """按文件名搜索"""

        # 构建查询
        search_query = db.query(File)

        # 文件名搜索（支持模糊匹配）
        search_terms = query.split()
        for term in search_terms:
            search_query = search_query.filter(
                File.file_name.contains(term)
            )

        # 文件类型过滤
        if file_type:
            if file_type == "image":
                search_query = search_query.filter(File.is_image == True)
            elif file_type == "document":
                search_query = search_query.filter(
                    File.file_type.contains("document")
                )
            elif file_type == "video":
                search_query = search_query.filter(
                    File.file_type.contains("video")
                )

        # 文件夹过滤
        if folder_path:
            search_query = search_query.filter(
                File.folder_path.contains(folder_path)
            )

        # 按相关性排序（访问次数 + 文件名匹配度）
        search_query = search_query.order_by(
            desc(File.access_count),
            File.file_name
        )

        files = search_query.limit(limit).all()

        # 计算相关性分数
        results = []
        for file in files:
            score = FileSearchEngine._calculate_relevance_score(query, file)

            result = {
                **file.to_dict(),
                "score": score,
                "highlight": FileSearchEngine._highlight_matches(query, file.file_name)
            }
            results.append(result)

        # 按分数排序
        results.sort(key=lambda x: x["score"], reverse=True)

        return results

    @staticmethod
    async def search_by_content(
        query: str,
        db: Session,
        user_id: int,
        limit: int = 50
    ) -> List[Dict]:
        """按文件内容搜索（简化实现）"""

        # 这里是简化实现，实际需要全文搜索引擎如Elasticsearch
        # 目前只搜索文件名和路径
        search_query = db.query(File).filter(
            or_(
                File.file_name.contains(query),
                File.file_path.contains(query)
            )
        ).order_by(desc(File.access_count)).limit(limit)

        files = search_query.all()

        results = []
        for file in files:
            score = FileSearchEngine._calculate_relevance_score(query, file)
            result = {
                **file.to_dict(),
                "score": score,
                "highlight": FileSearchEngine._highlight_matches(query, file.file_name)
            }
            results.append(result)

        return results

    @staticmethod
    async def search_by_image(
        query: str,
        db: Session,
        user_id: int,
        limit: int = 50
    ) -> List[Dict]:
        """图像识别搜索（模拟实现）"""

        # 这里是模拟实现，实际需要OpenCV或其他图像识别库
        # 目前只搜索图像文件
        search_query = db.query(File).filter(
            and_(
                File.is_image == True,
                or_(
                    File.file_name.contains(query),
                    File.file_path.contains(query)
                )
            )
        ).order_by(desc(File.access_count)).limit(limit)

        files = search_query.all()

        results = []
        for file in files:
            # 模拟图像识别分数
            score = FileSearchEngine._calculate_image_score(query, file)
            result = {
                **file.to_dict(),
                "score": score,
                "highlight": f"图像识别: {query}"
            }
            results.append(result)

        return results

    @staticmethod
    def _calculate_relevance_score(query: str, file: File) -> float:
        """计算相关性分数"""
        score = 0.0
        query_lower = query.lower()
        filename_lower = file.file_name.lower()

        # 完全匹配
        if query_lower == filename_lower:
            score += 1.0

        # 开头匹配
        elif filename_lower.startswith(query_lower):
            score += 0.8

        # 包含匹配
        elif query_lower in filename_lower:
            score += 0.6

        # 单词匹配
        query_words = query_lower.split()
        filename_words = filename_lower.split()

        for query_word in query_words:
            for filename_word in filename_words:
                if query_word == filename_word:
                    score += 0.3
                elif query_word in filename_word:
                    score += 0.1

        # 访问次数加权
        score += min(file.access_count / 100.0, 0.2)

        return min(score, 1.0)

    @staticmethod
    def _calculate_image_score(query: str, file: File) -> float:
        """计算图像识别分数（模拟）"""
        # 这里是模拟实现
        base_score = FileSearchEngine._calculate_relevance_score(query, file)

        # 模拟图像识别置信度
        image_confidence = 0.7  # 模拟70%置信度

        return base_score * image_confidence

    @staticmethod
    def _highlight_matches(query: str, text: str) -> str:
        """高亮匹配文本"""
        if not query or not text:
            return text

        # 简单的高亮实现
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        return pattern.sub(f"<mark>{query}</mark>", text)


# ==================== 搜索API ====================

@router.get("/files", response_model=SearchResponse)
async def search_files(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    engine: str = Query("filename", description="搜索引擎类型"),
    file_type: Optional[str] = Query(None, description="文件类型过滤"),
    folder_path: Optional[str] = Query(None, description="文件夹路径过滤"),
    limit: int = Query(50, ge=1, le=200, description="结果数量限制"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """搜索文件"""

    start_time = time.time()

    # 验证搜索引擎类型
    valid_engines = ["filename", "content", "image"]
    if engine not in valid_engines:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的搜索引擎类型，支持: {', '.join(valid_engines)}"
        )

    # 执行搜索
    results = []

    try:
        if engine == "filename":
            results = await FileSearchEngine.search_by_filename(
                q, db, current_user.id, limit, file_type, folder_path
            )
        elif engine == "content":
            results = await FileSearchEngine.search_by_content(
                q, db, current_user.id, limit
            )
        elif engine == "image":
            results = await FileSearchEngine.search_by_image(
                q, db, current_user.id, limit
            )

        search_time = time.time() - start_time

        # 记录搜索历史
        search_history = SearchHistory(
            user_id=current_user.id,
            search_query=q,
            search_type=SearchType.FILENAME if engine == "filename" else
                       SearchType.CONTENT if engine == "content" else SearchType.IMAGE,
            result_count=len(results),
            search_time_ms=int(search_time * 1000),
            ip_address="system"
        )
        db.add(search_history)
        db.commit()

        # 记录访问日志
        AccessLogger.log_search(current_user.id, q, len(results), search_time, "system")

        # 获取搜索建议
        suggestions = await get_search_suggestions_internal(q, db)

        return SearchResponse(
            query=q,
            engine=engine,
            results=[SearchResult(**result) for result in results],
            total=len(results),
            search_time=search_time,
            suggestions=suggestions,
            filters={
                "file_type": file_type,
                "folder_path": folder_path,
                "available_types": ["image", "document", "video", "other"]
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


async def get_search_suggestions_internal(query: str, db: Session) -> List[str]:
    """获取搜索建议（内部函数）"""
    suggestions = []

    # 基于历史搜索的建议
    recent_searches = db.query(SearchHistory.search_query).filter(
        SearchHistory.search_query.contains(query)
    ).distinct().limit(5).all()

    for search in recent_searches:
        if search.search_query != query:
            suggestions.append(search.search_query)

    # 基于文件名的建议
    similar_files = db.query(File.file_name).filter(
        File.file_name.contains(query)
    ).distinct().limit(5).all()

    for file in similar_files:
        # 提取文件名中的关键词
        filename_without_ext = os.path.splitext(file.file_name)[0]
        if filename_without_ext not in suggestions and len(suggestions) < 10:
            suggestions.append(filename_without_ext)

    # 添加一些通用建议
    if query:
        generic_suggestions = [
            f"{query} 图片",
            f"{query} 文档",
            f"{query} 视频",
            f"最新 {query}"
        ]

        for suggestion in generic_suggestions:
            if suggestion not in suggestions and len(suggestions) < 10:
                suggestions.append(suggestion)

    return suggestions[:10]


@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取搜索建议"""

    suggestions = await get_search_suggestions_internal(q, db)

    return {
        "query": q,
        "suggestions": suggestions
    }


@router.get("/history")
async def get_search_history(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取搜索历史"""

    query = db.query(SearchHistory).filter(
        SearchHistory.user_id == current_user.id
    ).order_by(desc(SearchHistory.created_at))

    total = query.count()
    histories = query.offset((page - 1) * size).limit(size).all()

    history_list = []
    for history in histories:
        history_dict = {
            "id": history.id,
            "search_query": history.search_query,
            "search_type": history.search_type.value if history.search_type else None,
            "result_count": history.result_count,
            "search_time_ms": history.search_time_ms,
            "created_at": history.created_at.isoformat() if history.created_at else None
        }
        history_list.append(history_dict)

    return {
        "histories": history_list,
        "total": total,
        "page": page,
        "size": size,
        "pages": (total + size - 1) // size
    }


@router.delete("/history/{history_id}")
async def delete_search_history(
    history_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除搜索历史"""

    history = db.query(SearchHistory).filter(
        and_(
            SearchHistory.id == history_id,
            SearchHistory.user_id == current_user.id
        )
    ).first()

    if not history:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="搜索历史不存在"
        )

    db.delete(history)
    db.commit()

    return {"message": "搜索历史删除成功"}


@router.delete("/history")
async def clear_search_history(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """清空搜索历史"""

    db.query(SearchHistory).filter(
        SearchHistory.user_id == current_user.id
    ).delete()

    db.commit()

    return {"message": "搜索历史清空成功"}


@router.get("/popular")
async def get_popular_searches(
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    limit: int = Query(10, ge=1, le=50, description="结果数量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取热门搜索"""

    from datetime import timedelta

    start_date = datetime.utcnow() - timedelta(days=days)

    # 统计热门搜索词
    popular_searches = db.query(
        SearchHistory.search_query,
        func.count(SearchHistory.id).label('search_count')
    ).filter(
        SearchHistory.created_at >= start_date
    ).group_by(
        SearchHistory.search_query
    ).order_by(
        desc(func.count(SearchHistory.id))
    ).limit(limit).all()

    results = []
    for search in popular_searches:
        results.append({
            "query": search.search_query,
            "count": search.search_count
        })

    return {
        "popular_searches": results,
        "period_days": days,
        "total": len(results)
    }


@router.get("/stats")
async def get_search_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取搜索统计"""

    # 用户搜索统计
    user_stats = db.query(
        func.count(SearchHistory.id).label('total_searches'),
        func.avg(SearchHistory.search_time_ms).label('avg_search_time'),
        func.sum(SearchHistory.result_count).label('total_results')
    ).filter(
        SearchHistory.user_id == current_user.id
    ).first()

    # 按搜索类型统计
    type_stats = db.query(
        SearchHistory.search_type,
        func.count(SearchHistory.id).label('count')
    ).filter(
        SearchHistory.user_id == current_user.id
    ).group_by(
        SearchHistory.search_type
    ).all()

    type_distribution = {}
    for stat in type_stats:
        type_distribution[stat.search_type.value if stat.search_type else 'unknown'] = stat.count

    return {
        "user_stats": {
            "total_searches": user_stats.total_searches or 0,
            "avg_search_time_ms": float(user_stats.avg_search_time or 0),
            "total_results": user_stats.total_results or 0
        },
        "type_distribution": type_distribution
    }
