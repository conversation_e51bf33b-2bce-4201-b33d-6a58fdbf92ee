"""
文件管理API路由 - 完整功能实现
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File as FastAPIFile, Response
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import os
import mimetypes
import hashlib
from pathlib import Path
import shutil
import zipfile
import io

from ..database import get_db
from ..models.user import User
from ..models.file import File
from ..models.log import UserLog, DownloadRecord, ActionType, DownloadType
from ..models.permission import FolderPermission
from ..api.auth import get_current_user
from ..core.security import FileHasher, EncryptionManager
from ..core.logging import AccessLogger, SecurityLogger
from ..config import settings

router = APIRouter()


# ==================== 辅助函数 ====================

async def check_folder_permission(user_id: int, folder_path: str, permission_type: str, db: Session) -> bool:
    """检查文件夹权限"""
    # 查询用户权限
    permission = db.query(FolderPermission).filter(
        and_(
            FolderPermission.user_id == user_id,
            FolderPermission.folder_path == folder_path
        )
    ).first()

    if not permission:
        # 如果没有特定权限，检查用户组默认权限
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return False

        # 管理员有所有权限
        if user.is_admin:
            return True

        # 根据用户组判断默认权限
        if permission_type == "read":
            return user.user_group.value in ["readonly", "download", "upload", "modify"]
        elif permission_type == "download":
            return user.user_group.value in ["download", "upload", "modify"]
        elif permission_type == "upload":
            return user.user_group.value in ["upload", "modify"]
        elif permission_type == "modify":
            return user.user_group.value in ["modify"]
        elif permission_type == "delete":
            return user.user_group.value in ["modify"]

        return False

    # 检查具体权限
    if permission_type == "read":
        return permission.can_read
    elif permission_type == "download":
        return permission.can_download
    elif permission_type == "upload":
        return permission.can_upload
    elif permission_type == "modify":
        return permission.can_modify
    elif permission_type == "delete":
        return permission.can_delete

    return False


async def get_folder_permissions(user_id: int, folder_path: str, db: Session) -> Dict[str, bool]:
    """获取文件夹权限"""
    permissions = {
        "read": await check_folder_permission(user_id, folder_path, "read", db),
        "download": await check_folder_permission(user_id, folder_path, "download", db),
        "upload": await check_folder_permission(user_id, folder_path, "upload", db),
        "modify": await check_folder_permission(user_id, folder_path, "modify", db),
        "delete": await check_folder_permission(user_id, folder_path, "delete", db),
    }
    return permissions


# ==================== 数据模型 ====================

class FileInfoResponse(BaseModel):
    """文件信息响应"""
    id: int
    file_name: str
    file_path: str
    file_size: int
    size_mb: float
    file_type: Optional[str]
    mime_type: Optional[str]
    is_image: bool
    is_sensitive: bool
    folder_path: str
    extension: str
    created_at: str
    updated_at: Optional[str]
    last_accessed: Optional[str]
    access_count: int
    thumbnail_path: Optional[str]


class FolderInfoResponse(BaseModel):
    """文件夹信息响应"""
    name: str
    path: str
    is_folder: bool
    size: int
    file_count: int
    modified_time: str
    permissions: Dict[str, bool]


class UploadResponse(BaseModel):
    """上传响应"""
    file_id: int
    file_name: str
    file_size: int
    file_path: str
    message: str


# ==================== 文件列表和浏览 ====================

@router.get("/browse")
async def browse_files(
    path: str = Query("/", description="浏览路径"),
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=200),
    file_type: Optional[str] = Query(None, description="文件类型过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: str = Query("name", description="排序字段"),
    sort_order: str = Query("asc", description="排序方向"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """浏览文件和文件夹"""

    # 检查路径权限
    if not await check_folder_permission(current_user.id, path, "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此文件夹"
        )

    try:
        # 构建实际路径
        actual_path = Path(settings.shared_folders_root) / path.lstrip("/")

        if not actual_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="路径不存在"
            )

        items = []
        total_size = 0

        # 遍历目录
        for item in actual_path.iterdir():
            try:
                stat = item.stat()

                # 文件夹
                if item.is_dir():
                    folder_info = {
                        "name": item.name,
                        "path": str(Path(path) / item.name),
                        "is_folder": True,
                        "size": 0,
                        "file_count": len(list(item.iterdir())) if item.is_dir() else 0,
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "permissions": await get_folder_permissions(current_user.id, str(Path(path) / item.name), db)
                    }
                    items.append(folder_info)

                # 文件
                elif item.is_file():
                    # 搜索过滤
                    if search and search.lower() not in item.name.lower():
                        continue

                    # 文件类型过滤
                    if file_type:
                        mime_type, _ = mimetypes.guess_type(str(item))
                        if file_type == "image" and not (mime_type and mime_type.startswith("image")):
                            continue
                        elif file_type == "document" and not (mime_type and ("document" in mime_type or "text" in mime_type)):
                            continue
                        elif file_type == "video" and not (mime_type and mime_type.startswith("video")):
                            continue

                    # 查询数据库中的文件信息
                    file_record = db.query(File).filter(
                        File.file_path == str(Path(path) / item.name)
                    ).first()

                    mime_type, _ = mimetypes.guess_type(str(item))

                    file_info = {
                        "id": file_record.id if file_record else 0,
                        "file_name": item.name,
                        "file_path": str(Path(path) / item.name),
                        "file_size": stat.st_size,
                        "size_mb": stat.st_size / (1024 * 1024),
                        "file_type": mime_type,
                        "mime_type": mime_type,
                        "is_image": mime_type and mime_type.startswith("image") if mime_type else False,
                        "is_sensitive": file_record.is_sensitive if file_record else False,
                        "folder_path": path,
                        "extension": item.suffix.lower(),
                        "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "updated_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "last_accessed": file_record.last_accessed.isoformat() if file_record and file_record.last_accessed else None,
                        "access_count": file_record.access_count if file_record else 0,
                        "thumbnail_path": file_record.thumbnail_path if file_record else None,
                        "is_folder": False
                    }
                    items.append(file_info)
                    total_size += stat.st_size

            except (OSError, PermissionError):
                continue

        # 排序
        reverse = sort_order.lower() == "desc"
        if sort_by == "name":
            items.sort(key=lambda x: x["name"].lower(), reverse=reverse)
        elif sort_by == "size":
            items.sort(key=lambda x: x.get("file_size", 0), reverse=reverse)
        elif sort_by == "modified":
            items.sort(key=lambda x: x.get("modified_time", x.get("updated_at", "")), reverse=reverse)

        # 文件夹优先
        folders = [item for item in items if item.get("is_folder", False)]
        files = [item for item in items if not item.get("is_folder", False)]
        items = folders + files

        # 分页
        total = len(items)
        start_idx = (page - 1) * size
        end_idx = start_idx + size
        paginated_items = items[start_idx:end_idx]

        # 记录访问日志
        AccessLogger.log_request("GET", f"/files/browse?path={path}", 200, 0,
                                current_user.id, "file_browser")

        return {
            "items": paginated_items,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size,
            "path": path,
            "total_size": total_size,
            "permissions": await get_folder_permissions(current_user.id, path, db)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"浏览文件失败: {str(e)}"
        )


@router.get("/info/{file_id}")
async def get_file_info(
    file_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取文件详细信息"""

    file_record = db.query(File).filter(File.id == file_id).first()
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 检查权限
    if not await check_folder_permission(current_user.id, file_record.folder_path, "read", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限访问此文件"
        )

    # 更新访问记录
    file_record.access_count += 1
    file_record.last_accessed = datetime.utcnow()
    db.commit()

    # 记录访问日志
    log_entry = UserLog(
        user_id=current_user.id,
        username=current_user.username,
        action=ActionType.VIEW,
        target_file=file_record.file_name,
        target_folder=file_record.folder_path,
        ip_address="system",
        success=True
    )
    db.add(log_entry)
    db.commit()

    return FileInfoResponse(**file_record.to_dict())


# ==================== 文件下载 ====================

@router.get("/download/{file_id}")
async def download_file(
    file_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """下载单个文件"""

    # 查询文件记录
    file_record = db.query(File).filter(File.id == file_id).first()
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )

    # 检查下载权限
    if not await check_folder_permission(current_user.id, file_record.folder_path, "download", db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限下载此文件"
        )

    # 检查下载配额
    file_size_mb = int(file_record.file_size / (1024 * 1024))
    if not current_user.can_download(file_size_mb):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"下载配额不足，需要 {file_size_mb}MB，剩余 {current_user.remaining_quota}MB"
        )

    # 构建文件路径
    file_path = Path(settings.shared_folders_root) / file_record.file_path.lstrip("/")

    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在于文件系统中"
        )

    # 更新用户配额
    current_user.add_download_usage(file_size_mb)
    db.commit()

    # 记录下载记录
    download_record = DownloadRecord(
        user_id=current_user.id,
        file_id=file_record.id,
        file_path=file_record.file_path,
        download_size=file_record.file_size,
        download_type=DownloadType.SINGLE,
        ip_address="system"
    )
    db.add(download_record)

    # 更新文件访问记录
    file_record.access_count += 1
    file_record.last_accessed = datetime.utcnow()

    db.commit()

    # 记录访问日志
    AccessLogger.log_download(current_user.id, file_record.file_path, file_record.file_size, "system")

    # 返回文件
    return FileResponse(
        path=str(file_path),
        filename=file_record.file_name,
        media_type=file_record.mime_type or 'application/octet-stream'
    )


@router.post("/download/batch")
async def download_batch_files(
    file_ids: List[int],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量下载文件（打包为ZIP）"""

    if len(file_ids) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="一次最多下载100个文件"
        )

    # 查询文件记录
    file_records = db.query(File).filter(File.id.in_(file_ids)).all()
    if not file_records:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="没有找到要下载的文件"
        )

    # 检查权限和配额
    total_size = 0
    valid_files = []

    for file_record in file_records:
        # 检查权限
        if not await check_folder_permission(current_user.id, file_record.folder_path, "download", db):
            continue

        # 检查文件是否存在
        file_path = Path(settings.shared_folders_root) / file_record.file_path.lstrip("/")
        if not file_path.exists():
            continue

        valid_files.append((file_record, file_path))
        total_size += file_record.file_size

    if not valid_files:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有可下载的文件"
        )

    # 检查配额
    total_size_mb = int(total_size / (1024 * 1024))
    if not current_user.can_download(total_size_mb):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"下载配额不足，需要 {total_size_mb}MB，剩余 {current_user.remaining_quota}MB"
        )

    # 创建ZIP文件
    zip_buffer = io.BytesIO()

    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for file_record, file_path in valid_files:
            try:
                # 添加文件到ZIP
                arcname = f"{file_record.folder_path.strip('/')}/{file_record.file_name}"
                zip_file.write(str(file_path), arcname)

                # 记录下载记录
                download_record = DownloadRecord(
                    user_id=current_user.id,
                    file_id=file_record.id,
                    file_path=file_record.file_path,
                    download_size=file_record.file_size,
                    download_type=DownloadType.BATCH,
                    ip_address="system"
                )
                db.add(download_record)

                # 更新文件访问记录
                file_record.access_count += 1
                file_record.last_accessed = datetime.utcnow()

            except Exception as e:
                continue

    # 更新用户配额
    current_user.add_download_usage(total_size_mb)
    db.commit()

    # 记录访问日志
    AccessLogger.log_download(current_user.id, f"批量下载({len(valid_files)}个文件)", total_size, "system")

    zip_buffer.seek(0)

    # 返回ZIP文件
    return StreamingResponse(
        io.BytesIO(zip_buffer.read()),
        media_type="application/zip",
        headers={"Content-Disposition": f"attachment; filename=files_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"}
    )
