-- 企业级文件共享系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS fileshare_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE fileshare_system;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(100) COMMENT '邮箱',
    full_name VARCHAR(100) COMMENT '真实姓名',
    user_group ENUM('readonly', 'download', 'upload', 'modify', 'admin') DEFAULT 'readonly' COMMENT '用户组',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_admin BOOLEAN DEFAULT FALSE COMMENT '是否管理员',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    login_attempts INT DEFAULT 0 COMMENT '登录尝试次数',
    locked_until TIMESTAMP NULL COMMENT '锁定到期时间',
    download_quota INT DEFAULT 500 COMMENT '每日下载配额(MB)',
    daily_download_count INT DEFAULT 0 COMMENT '今日下载量(MB)',
    download_reset_date DATE DEFAULT (CURDATE()) COMMENT '下载量重置日期',
    INDEX idx_username (username),
    INDEX idx_user_group (user_group),
    INDEX idx_is_active (is_active),
    INDEX idx_last_login (last_login)
) COMMENT='用户表';

-- 文件信息表
CREATE TABLE files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_path VARCHAR(1000) NOT NULL COMMENT '文件完整路径',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_hash VARCHAR(64) COMMENT 'SHA256哈希',
    thumbnail_path VARCHAR(500) COMMENT '缩略图路径',
    is_image BOOLEAN DEFAULT FALSE COMMENT '是否为图片',
    is_sensitive BOOLEAN DEFAULT FALSE COMMENT '是否为敏感文件',
    folder_path VARCHAR(1000) NOT NULL COMMENT '所在文件夹路径',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_accessed TIMESTAMP NULL COMMENT '最后访问时间',
    access_count INT DEFAULT 0 COMMENT '访问次数',
    INDEX idx_file_name (file_name),
    INDEX idx_file_type (file_type),
    INDEX idx_file_hash (file_hash),
    INDEX idx_folder_path (folder_path(255)),
    INDEX idx_is_image (is_image),
    INDEX idx_is_sensitive (is_sensitive),
    FULLTEXT idx_file_name_fulltext (file_name)
) COMMENT='文件信息表';

-- 文件夹权限表
CREATE TABLE folder_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    folder_path VARCHAR(1000) NOT NULL COMMENT '文件夹路径',
    can_read BOOLEAN DEFAULT TRUE COMMENT '可读权限',
    can_download BOOLEAN DEFAULT FALSE COMMENT '可下载权限',
    can_upload BOOLEAN DEFAULT FALSE COMMENT '可上传权限',
    can_modify BOOLEAN DEFAULT FALSE COMMENT '可修改权限',
    can_delete BOOLEAN DEFAULT FALSE COMMENT '可删除权限',
    internal_access BOOLEAN DEFAULT TRUE COMMENT '内网访问权限',
    external_access BOOLEAN DEFAULT FALSE COMMENT '外网访问权限',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_folder (user_id, folder_path(255)),
    INDEX idx_user_id (user_id),
    INDEX idx_folder_path (folder_path(255))
) COMMENT='文件夹权限表';

-- 用户操作日志表
CREATE TABLE user_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名(冗余字段)',
    action ENUM('login', 'logout', 'search', 'download', 'upload', 'view', 'delete', 'modify') NOT NULL COMMENT '操作类型',
    target_file VARCHAR(1000) COMMENT '目标文件',
    target_folder VARCHAR(1000) COMMENT '目标文件夹',
    search_query VARCHAR(500) COMMENT '搜索关键词',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    success BOOLEAN DEFAULT TRUE COMMENT '操作是否成功',
    error_message TEXT COMMENT '错误信息',
    file_size BIGINT COMMENT '文件大小(用于下载统计)',
    duration_ms INT COMMENT '操作耗时(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_action (user_id, action),
    INDEX idx_created_at (created_at),
    INDEX idx_action (action),
    INDEX idx_ip_address (ip_address),
    INDEX idx_success (success)
) COMMENT='用户操作日志表';

-- 下载记录表
CREATE TABLE download_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    file_id INT NOT NULL COMMENT '文件ID',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件路径',
    download_size BIGINT NOT NULL COMMENT '下载大小(字节)',
    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
    encryption_password VARCHAR(100) COMMENT '加密密码',
    password_requested BOOLEAN DEFAULT FALSE COMMENT '是否申请密码',
    password_approved BOOLEAN DEFAULT FALSE COMMENT '密码是否批准',
    password_request_count INT DEFAULT 0 COMMENT '密码申请次数',
    download_type ENUM('single', 'batch', 'folder') DEFAULT 'single' COMMENT '下载类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    INDEX idx_user_download (user_id, created_at),
    INDEX idx_file_download (file_id, created_at),
    INDEX idx_is_encrypted (is_encrypted),
    INDEX idx_password_requested (password_requested)
) COMMENT='下载记录表';

-- 系统配置表
CREATE TABLE system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(前端可访问)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) COMMENT='系统配置表';

-- 搜索历史表
CREATE TABLE search_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '用户ID',
    search_query VARCHAR(500) NOT NULL COMMENT '搜索关键词',
    search_type ENUM('filename', 'image', 'content') DEFAULT 'filename' COMMENT '搜索类型',
    result_count INT DEFAULT 0 COMMENT '结果数量',
    search_time_ms INT COMMENT '搜索耗时(毫秒)',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_search (user_id, created_at),
    INDEX idx_search_query (search_query(100)),
    INDEX idx_search_type (search_type),
    INDEX idx_created_at (created_at)
) COMMENT='搜索历史表';

-- 系统通知表
CREATE TABLE system_notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    notification_type ENUM('info', 'warning', 'error', 'success') DEFAULT 'info' COMMENT '通知类型',
    target_users ENUM('all', 'admin', 'specific') DEFAULT 'all' COMMENT '目标用户',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_is_active (is_active),
    INDEX idx_target_users (target_users),
    INDEX idx_start_end_time (start_time, end_time)
) COMMENT='系统通知表';

-- 插入默认管理员账户
INSERT INTO users (username, password_hash, email, full_name, user_group, is_admin, is_active) 
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6QJw.2Qjaa', '<EMAIL>', '系统管理员', 'admin', TRUE, TRUE);
-- 默认密码: admin123 (请在生产环境中修改)

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description, is_public) VALUES
('system_name', '企业级文件共享系统', 'string', '系统名称', TRUE),
('system_version', '1.0.0', 'string', '系统版本', TRUE),
('max_file_size', '104857600', 'int', '最大文件大小(字节)', FALSE),
('max_daily_download', '1073741824', 'int', '每日最大下载量(字节)', FALSE),
('encryption_threshold', '3', 'int', '加密下载阈值', FALSE),
('enable_external_access', 'false', 'boolean', '启用外网访问', FALSE),
('enable_image_search', 'true', 'boolean', '启用图像搜索', FALSE),
('enable_everything_search', 'true', 'boolean', '启用Everything搜索', FALSE),
('max_search_results', '1000', 'int', '最大搜索结果数', FALSE),
('session_timeout', '1800', 'int', '会话超时时间(秒)', FALSE);

-- 插入默认通知
INSERT INTO system_notifications (title, content, notification_type, target_users) 
VALUES ('欢迎使用文件共享系统', '系统已成功部署，请注意文件安全和访问权限管理。', 'info', 'all');

-- 创建视图：用户统计
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.user_group,
    u.is_active,
    u.last_login,
    COUNT(DISTINCT ul.id) as total_operations,
    COUNT(DISTINCT CASE WHEN ul.action = 'download' THEN ul.id END) as download_count,
    COUNT(DISTINCT CASE WHEN ul.action = 'search' THEN ul.id END) as search_count,
    COALESCE(SUM(CASE WHEN ul.action = 'download' THEN ul.file_size END), 0) as total_download_size
FROM users u
LEFT JOIN user_logs ul ON u.id = ul.user_id
GROUP BY u.id, u.username, u.user_group, u.is_active, u.last_login;

-- 创建视图：文件统计
CREATE VIEW file_stats AS
SELECT 
    f.id,
    f.file_name,
    f.file_type,
    f.file_size,
    f.folder_path,
    f.access_count,
    f.last_accessed,
    COUNT(DISTINCT dr.id) as download_count,
    COUNT(DISTINCT dr.user_id) as unique_downloaders
FROM files f
LEFT JOIN download_records dr ON f.id = dr.file_id
GROUP BY f.id, f.file_name, f.file_type, f.file_size, f.folder_path, f.access_count, f.last_accessed;

-- 创建索引优化查询性能
CREATE INDEX idx_user_logs_composite ON user_logs(user_id, action, created_at);
CREATE INDEX idx_download_records_composite ON download_records(user_id, created_at, download_size);
CREATE INDEX idx_files_composite ON files(folder_path(255), file_type, is_image);

COMMIT;
