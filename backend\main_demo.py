"""
企业级文件共享系统 - 演示版本（无数据库）
"""
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import logging

# 设置基础日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="企业级文件共享系统",
    version="1.0.0",
    description="企业级文件共享系统 - 安全、高效、易用的文件管理解决方案",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)


# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": time.time(),
            "path": str(request.url.path)
        }
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0",
        "database": "demo_mode",
        "redis": "demo_mode",
        "debug": True,
    }


# 系统信息端点
@app.get("/info")
async def system_info():
    """系统信息"""
    import platform
    
    return {
        "app_name": "企业级文件共享系统",
        "app_version": "1.0.0",
        "mode": "演示模式",
        "system": {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
        },
        "timestamp": time.time(),
    }


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用企业级文件共享系统",
        "version": "1.0.0",
        "mode": "演示模式",
        "docs": "/docs",
        "health": "/health",
        "info": "/info",
        "timestamp": time.time(),
    }


# 演示API端点
@app.get("/api/demo/login")
async def demo_login():
    """演示登录API"""
    return {
        "message": "这是演示模式的登录API",
        "access_token": "demo_token_12345",
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "username": "demo_user",
            "user_group": "admin",
            "is_admin": True
        }
    }


@app.get("/api/demo/files")
async def demo_files():
    """演示文件列表API"""
    return {
        "files": [
            {
                "id": 1,
                "file_name": "示例图片.jpg",
                "file_size": 2048000,
                "file_type": "image/jpeg",
                "is_image": True,
                "created_at": "2024-01-01T10:00:00"
            },
            {
                "id": 2,
                "file_name": "设计文档.pdf",
                "file_size": 1024000,
                "file_type": "application/pdf",
                "is_image": False,
                "created_at": "2024-01-01T11:00:00"
            },
            {
                "id": 3,
                "file_name": "产品图.psd",
                "file_size": 15728640,
                "file_type": "image/vnd.adobe.photoshop",
                "is_image": True,
                "created_at": "2024-01-01T12:00:00"
            }
        ],
        "total": 3,
        "message": "这是演示模式的文件列表"
    }


@app.get("/api/demo/search")
async def demo_search(q: str = ""):
    """演示搜索API"""
    return {
        "query": q,
        "results": [
            {
                "id": 1,
                "file_name": f"包含'{q}'的文件.jpg",
                "file_path": "/demo/images/file1.jpg",
                "file_size": 2048000,
                "file_type": "image/jpeg",
                "is_image": True,
                "score": 0.95
            }
        ],
        "total": 1,
        "search_time": 0.123,
        "message": "这是演示模式的搜索结果"
    }


@app.get("/api/demo/admin/dashboard")
async def demo_admin_dashboard():
    """演示管理员仪表板API"""
    return {
        "users": {
            "total": 25,
            "active": 20,
            "inactive": 5
        },
        "files": {
            "total": 1234,
            "images": 856,
            "documents": 378
        },
        "system": {
            "uptime": "运行中",
            "version": "1.0.0",
            "status": "healthy"
        },
        "message": "这是演示模式的管理员仪表板"
    }


def main():
    """主函数"""
    logger.info("启动演示服务器: 0.0.0.0:8000")
    
    uvicorn.run(
        "main_demo:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
        access_log=True,
    )


if __name__ == "__main__":
    main()
