<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理控制台测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 企业级文件共享系统</h1>
            <h2>管理员控制台</h2>
            <p>系统运行正常 - 测试页面</p>
        </div>

        <div class="card">
            <h2>✅ 前端服务状态</h2>
            <p><strong>前端服务:</strong> 运行中 (http://localhost:3001)</p>
            <p><strong>后端服务:</strong> 运行中 (http://localhost:8000)</p>
            <p><strong>构建工具:</strong> Vite</p>
            <p><strong>UI框架:</strong> React + Ant Design</p>
            
            <button class="btn" onclick="window.location.href='/'">返回主应用</button>
            <button class="btn" onclick="window.location.href='http://localhost:8000/docs'">查看API文档</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" style="color: #1890ff;">25</div>
                <div>总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #52c41a;">1,234</div>
                <div>总文件数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #faad14;">2.3GB</div>
                <div>今日下载量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #f5222d;">15</div>
                <div>在线用户</div>
            </div>
        </div>

        <div class="card">
            <h3>🚀 功能模块</h3>
            <ul>
                <li>✅ 系统概览 - 实时统计和监控</li>
                <li>✅ 用户管理 - 完整的用户CRUD操作</li>
                <li>✅ 文件管理 - 文件浏览和管理</li>
                <li>🔄 权限控制 - 权限配置（开发中）</li>
                <li>🔄 访问统计 - 数据分析（开发中）</li>
                <li>🔄 搜索引擎 - 搜索配置（开发中）</li>
                <li>🔄 系统设置 - 系统配置（开发中）</li>
                <li>🔄 安全监控 - 安全管理（开发中）</li>
                <li>🔄 操作日志 - 日志查看（开发中）</li>
                <li>🔄 网络设置 - 网络配置（开发中）</li>
            </ul>
        </div>

        <div class="card">
            <h3>🔧 技术栈</h3>
            <p><strong>前端:</strong> React 18 + TypeScript + Ant Design + Vite</p>
            <p><strong>后端:</strong> Python + FastAPI + MySQL + Redis</p>
            <p><strong>特性:</strong> 前后端分离、JWT认证、响应式设计</p>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            console.log('管理控制台测试页面加载完成');
            
            // 检查后端API状态
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    console.log('后端API状态:', data);
                })
                .catch(error => {
                    console.log('后端API连接失败:', error);
                });
        });
    </script>
</body>
</html>
