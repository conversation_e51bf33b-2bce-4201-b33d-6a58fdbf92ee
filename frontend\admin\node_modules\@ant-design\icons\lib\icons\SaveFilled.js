"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _SaveFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/SaveFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var SaveFilled = function SaveFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _SaveFilled.default
  }));
};

/**![save](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5My4zIDI5My4zTDczMC43IDEzMC43Yy0xMi0xMi0yOC4zLTE4LjctNDUuMy0xOC43SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYzMzguNWMwLTE3LTYuNy0zMy4yLTE4LjctNDUuMnpNMzg0IDE3NmgyNTZ2MTEySDM4NFYxNzZ6bTEyOCA1NTRjLTc5LjUgMC0xNDQtNjQuNS0xNDQtMTQ0czY0LjUtMTQ0IDE0NC0xNDQgMTQ0IDY0LjUgMTQ0IDE0NC02NC41IDE0NC0xNDQgMTQ0em0wLTIyNGMtNDQuMiAwLTgwIDM1LjgtODAgODBzMzUuOCA4MCA4MCA4MCA4MC0zNS44IDgwLTgwLTM1LjgtODAtODAtODB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(SaveFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SaveFilled';
}
var _default = exports.default = RefIcon;