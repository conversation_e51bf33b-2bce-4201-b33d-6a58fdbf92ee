"""
用户API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from ..database import get_db
from ..models.user import User
from ..api.auth import get_current_user

router = APIRouter()


class UserStats(BaseModel):
    """用户统计模型"""
    download_count: int
    upload_count: int
    search_count: int
    total_download_size: int


@router.get("/profile")
async def get_user_profile(
    current_user: User = Depends(get_current_user)
):
    """获取用户资料"""
    return current_user.to_dict()


@router.get("/stats")
async def get_user_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户统计信息"""
    
    # 示例统计数据
    stats = {
        "download_count": 25,
        "upload_count": 5,
        "search_count": 150,
        "total_download_size": 1024 * 1024 * 500,  # 500MB
        "quota_usage": current_user.quota_percentage,
        "remaining_quota": current_user.remaining_quota
    }
    
    return stats


@router.get("/activity")
async def get_user_activity(
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户活动记录"""
    
    # 示例活动记录
    activities = [
        {
            "id": 1,
            "action": "download",
            "target": "示例文件.jpg",
            "timestamp": "2024-01-01T10:30:00",
            "success": True
        },
        {
            "id": 2,
            "action": "search",
            "target": "图片",
            "timestamp": "2024-01-01T10:25:00",
            "success": True
        }
    ]
    
    return {
        "activities": activities[:limit],
        "total": len(activities)
    }
