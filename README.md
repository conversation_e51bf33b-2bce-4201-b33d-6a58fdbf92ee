# 企业级文件共享系统

一个现代化、安全、高效的企业级文件共享解决方案，支持内外网访问控制、双搜索引擎、实时监控等功能。

## 🚀 主要特性

- **🔐 权限控制**: 细粒度的文件访问权限控制，支持只读、下载、上传、修改、删除等权限
- **🔍 双搜索引擎**: Everything级别的文件名搜索 + OpenCV图像识别搜索
- **🖼️ 多格式支持**: 支持JPG、PSD、TIF、AI、EPS、PNG等格式的缩略图生成
- **🔒 加密下载**: 可配置下载次数后自动加密，支持密码申请机制
- **📊 实时监控**: 用户行为记录、访问统计、安全监控、流量控制
- **🌐 内外网控制**: 灵活的网络访问控制，支持内网/外网独立配置
- **👥 用户管理**: 完善的用户管理系统，支持用户组、配额管理
- **📱 现代化UI**: 响应式设计，支持多种视图模式

## 🛠️ 技术栈

### 后端
- **Python 3.9+** - 主要开发语言
- **FastAPI** - 高性能Web框架
- **MySQL 8.0** - 主数据库
- **Redis** - 缓存和会话存储
- **SQLAlchemy** - ORM框架
- **OpenCV** - 图像处理和识别

### 前端
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全
- **Ant Design** - UI组件库
- **Vite** - 构建工具

## 📋 系统要求

- **操作系统**: Windows 10/11 或 Windows Server 2019+
- **Python**: 3.9或更高版本
- **MySQL**: 8.0或更高版本
- **内存**: 最小4GB，推荐8GB+
- **存储**: 系统盘50GB+，数据盘根据需求

## 🚀 快速开始

### 1. 环境准备

确保已安装以下软件：
- Python 3.9+
- MySQL 8.0+
- Git

### 2. 克隆项目

```bash
git clone <repository-url>
cd Net
```

### 3. 安装系统

```bash
cd backend
python setup.py install
```

安装程序会引导您完成以下步骤：
- 检查Python版本
- 配置MySQL连接
- 创建数据库
- 安装Python依赖
- 初始化数据库表
- 创建默认管理员账户

### 4. 配置系统

编辑 `backend/.env` 文件，配置系统参数：

```env
# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=123456
DATABASE_NAME=fileshare_system

# 文件存储配置
SHARED_FOLDERS_ROOT=C:/SharedFiles
THUMBNAILS_PATH=./data/thumbnails

# 安全配置
SECRET_KEY=your-super-secret-key
ENABLE_EXTERNAL_ACCESS=false
ALLOWED_IPS=127.0.0.1,***********/24
```

### 5. 启动服务

```bash
python main.py
```

或使用安装脚本：

```bash
python setup.py start
```

### 6. 访问系统

- **用户界面**: http://localhost:8000
- **管理员界面**: http://localhost:8000/admin
- **API文档**: http://localhost:8000/docs (开发模式)
- **健康检查**: http://localhost:8000/health

### 默认账户

- **管理员用户名**: admin
- **管理员密码**: admin123

⚠️ **重要**: 首次登录后请立即修改默认密码！

## 📖 使用指南

### 管理员功能

1. **用户管理**
   - 创建、编辑、删除用户
   - 设置用户组和权限
   - 管理用户配额

2. **文件管理**
   - 配置共享文件夹
   - 设置文件夹权限
   - 监控文件访问

3. **系统监控**
   - 实时用户活动
   - 下载统计
   - 安全告警

4. **系统设置**
   - 搜索引擎配置
   - 安全策略设置
   - 网络访问控制

### 用户功能

1. **文件浏览**
   - 多种视图模式（列表、大图标、详情）
   - 文件夹导航
   - 缩略图预览

2. **搜索功能**
   - 文件名搜索（Everything引擎）
   - 图像识别搜索
   - 高级筛选

3. **下载管理**
   - 单文件下载
   - 批量下载
   - 文件夹打包下载

## 🔧 高级配置

### 搜索引擎配置

1. **Everything搜索**
   ```env
   EVERYTHING_ENABLED=true
   EVERYTHING_PATH=C:/Program Files/Everything/Everything.exe
   ```

2. **图像识别搜索**
   ```env
   IMAGE_SEARCH_ENABLED=true
   ```

### 安全配置

1. **IP访问控制**
   ```env
   ALLOWED_IPS=127.0.0.1,***********/24,10.0.0.0/8
   ```

2. **登录安全**
   ```env
   MAX_LOGIN_ATTEMPTS=5
   LOCKOUT_DURATION=300
   ```

3. **加密下载**
   ```env
   ENCRYPTION_THRESHOLD=3
   PASSWORD_REQUEST_LIMIT=5
   ```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接信息
   - 确认数据库用户权限

2. **文件搜索不工作**
   - 检查Everything是否安装
   - 验证Everything路径配置
   - 确认共享文件夹权限

3. **缩略图不显示**
   - 检查缩略图目录权限
   - 验证图像处理库安装
   - 查看错误日志

### 日志查看

系统日志位于 `backend/data/logs/` 目录：
- `app.log` - 应用日志
- `error.log` - 错误日志
- `access.log` - 访问日志
- `security.log` - 安全日志

## 🛡️ 安全注意事项

1. **修改默认密码**: 首次部署后立即修改默认管理员密码
2. **配置防火墙**: 限制不必要的网络访问
3. **定期备份**: 定期备份数据库和配置文件
4. **更新系统**: 定期更新系统和依赖包
5. **监控日志**: 定期检查安全日志和访问日志

## 📚 开发文档

详细的开发文档请参考：
- [UI设计文档](docs/ui-design.html)
- [开发指南](docs/development-guide.html)
- [API文档](http://localhost:8000/docs) (开发模式)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证，详情请参考LICENSE文件。

## 📞 支持

如有问题或需要技术支持，请联系开发团队。

---

**企业级文件共享系统** - 安全、高效、易用的文件管理解决方案
