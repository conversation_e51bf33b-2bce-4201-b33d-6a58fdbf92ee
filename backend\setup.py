"""
系统安装和初始化脚本
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path
import mysql.connector
from mysql.connector import Error
import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table

console = Console()


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 9):
        console.print("[red]错误: 需要Python 3.9或更高版本[/red]")
        sys.exit(1)
    console.print(f"[green]✓[/green] Python版本: {sys.version}")


def check_mysql_connection(host, port, user, password):
    """检查MySQL连接"""
    try:
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password
        )
        if connection.is_connected():
            console.print(f"[green]✓[/green] MySQL连接成功")
            return connection
    except Error as e:
        console.print(f"[red]✗[/red] MySQL连接失败: {e}")
        return None


def create_database(connection, db_name):
    """创建数据库"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        console.print(f"[green]✓[/green] 数据库 '{db_name}' 创建成功")
        cursor.close()
        return True
    except Error as e:
        console.print(f"[red]✗[/red] 创建数据库失败: {e}")
        return False


def install_dependencies():
    """安装Python依赖"""
    console.print("[blue]正在安装Python依赖...[/blue]")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        console.print("[green]✓[/green] Python依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        console.print(f"[red]✗[/red] 依赖安装失败: {e}")
        return False


def create_env_file():
    """创建环境配置文件"""
    if Path(".env").exists():
        console.print("[yellow]⚠[/yellow] .env文件已存在，跳过创建")
        return True
    
    try:
        shutil.copy(".env.example", ".env")
        console.print("[green]✓[/green] 环境配置文件创建成功")
        console.print("[yellow]请编辑 .env 文件配置数据库连接信息[/yellow]")
        return True
    except Exception as e:
        console.print(f"[red]✗[/red] 创建环境配置文件失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    directories = [
        "data/thumbnails",
        "data/temp", 
        "data/logs",
        "static",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    console.print("[green]✓[/green] 目录结构创建成功")


def init_database():
    """初始化数据库"""
    console.print("[blue]正在初始化数据库...[/blue]")
    
    try:
        # 读取SQL文件
        sql_file = Path("database/init.sql")
        if not sql_file.exists():
            console.print("[red]✗[/red] 数据库初始化文件不存在")
            return False
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 执行SQL
        from app.config import settings
        connection = mysql.connector.connect(
            host=settings.database_host,
            port=settings.database_port,
            user=settings.database_user,
            password=settings.database_password,
            database=settings.database_name
        )
        
        cursor = connection.cursor()
        
        # 分割SQL语句并执行
        statements = sql_content.split(';')
        for statement in statements:
            statement = statement.strip()
            if statement:
                cursor.execute(statement)
        
        connection.commit()
        cursor.close()
        connection.close()
        
        console.print("[green]✓[/green] 数据库初始化成功")
        return True
        
    except Exception as e:
        console.print(f"[red]✗[/red] 数据库初始化失败: {e}")
        return False


@click.group()
def cli():
    """企业级文件共享系统安装工具"""
    pass


@cli.command()
@click.option('--host', default='localhost', help='MySQL主机地址')
@click.option('--port', default=3306, help='MySQL端口')
@click.option('--user', default='root', help='MySQL用户名')
@click.option('--password', prompt=True, hide_input=True, help='MySQL密码')
@click.option('--database', default='fileshare_system', help='数据库名称')
def install(host, port, user, password, database):
    """完整安装系统"""
    
    console.print(Panel.fit(
        "[bold blue]企业级文件共享系统安装程序[/bold blue]\n"
        "正在进行系统安装和配置...",
        border_style="blue"
    ))
    
    # 检查Python版本
    check_python_version()
    
    # 检查MySQL连接
    connection = check_mysql_connection(host, port, user, password)
    if not connection:
        console.print("[red]安装失败: 无法连接到MySQL[/red]")
        sys.exit(1)
    
    # 创建数据库
    if not create_database(connection, database):
        console.print("[red]安装失败: 无法创建数据库[/red]")
        sys.exit(1)
    
    connection.close()
    
    # 创建目录结构
    create_directories()
    
    # 创建环境配置文件
    create_env_file()
    
    # 安装依赖
    if not install_dependencies():
        console.print("[red]安装失败: 无法安装依赖[/red]")
        sys.exit(1)
    
    # 初始化数据库
    if not init_database():
        console.print("[red]安装失败: 无法初始化数据库[/red]")
        sys.exit(1)
    
    # 安装完成
    console.print(Panel.fit(
        "[bold green]安装完成！[/bold green]\n\n"
        "下一步:\n"
        "1. 编辑 .env 文件配置系统参数\n"
        "2. 运行 'python main.py' 启动服务\n"
        "3. 访问 http://localhost:8000 查看系统\n\n"
        "默认管理员账户:\n"
        "用户名: admin\n"
        "密码: admin123",
        border_style="green"
    ))


@cli.command()
def check():
    """检查系统状态"""
    console.print("[blue]正在检查系统状态...[/blue]")
    
    table = Table(title="系统状态检查")
    table.add_column("项目", style="cyan")
    table.add_column("状态", style="magenta")
    table.add_column("说明", style="green")
    
    # 检查Python版本
    python_ok = sys.version_info >= (3, 9)
    table.add_row(
        "Python版本",
        "✓ 正常" if python_ok else "✗ 错误",
        f"{sys.version}" if python_ok else "需要Python 3.9+"
    )
    
    # 检查依赖文件
    requirements_ok = Path("requirements.txt").exists()
    table.add_row(
        "依赖文件",
        "✓ 存在" if requirements_ok else "✗ 缺失",
        "requirements.txt"
    )
    
    # 检查环境配置
    env_ok = Path(".env").exists()
    table.add_row(
        "环境配置",
        "✓ 存在" if env_ok else "✗ 缺失",
        ".env文件"
    )
    
    # 检查数据库配置
    db_init_ok = Path("database/init.sql").exists()
    table.add_row(
        "数据库脚本",
        "✓ 存在" if db_init_ok else "✗ 缺失",
        "database/init.sql"
    )
    
    # 检查目录结构
    dirs_ok = all(Path(d).exists() for d in ["data/thumbnails", "data/temp", "data/logs"])
    table.add_row(
        "目录结构",
        "✓ 正常" if dirs_ok else "✗ 缺失",
        "data目录及子目录"
    )
    
    console.print(table)
    
    if all([python_ok, requirements_ok, env_ok, db_init_ok, dirs_ok]):
        console.print("[green]✓ 系统状态正常，可以启动服务[/green]")
    else:
        console.print("[red]✗ 系统状态异常，请运行安装程序[/red]")


@cli.command()
def start():
    """启动服务"""
    console.print("[blue]正在启动服务...[/blue]")
    
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        console.print("\n[yellow]服务已停止[/yellow]")
    except Exception as e:
        console.print(f"[red]启动失败: {e}[/red]")


@cli.command()
@click.option('--username', prompt=True, help='管理员用户名')
@click.option('--password', prompt=True, hide_input=True, help='管理员密码')
@click.option('--email', help='管理员邮箱')
def create_admin(username, password, email):
    """创建管理员账户"""
    console.print("[blue]正在创建管理员账户...[/blue]")
    
    try:
        from app.database import SessionLocal
        from app.models.user import User
        from app.core.security import PasswordManager
        
        db = SessionLocal()
        
        # 检查用户是否已存在
        existing_user = db.query(User).filter(User.username == username).first()
        if existing_user:
            console.print(f"[red]✗[/red] 用户 '{username}' 已存在")
            return
        
        # 创建管理员用户
        password_hash = PasswordManager.get_password_hash(password)
        admin_user = User.create_admin(
            username=username,
            password_hash=password_hash,
            email=email,
            full_name="系统管理员"
        )
        
        db.add(admin_user)
        db.commit()
        
        console.print(f"[green]✓[/green] 管理员账户 '{username}' 创建成功")
        
    except Exception as e:
        console.print(f"[red]✗[/red] 创建管理员账户失败: {e}")


if __name__ == "__main__":
    cli()
