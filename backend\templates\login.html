<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 企业级文件共享系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .login-header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            font-weight: 600;
        }
        .login-header p {
            margin: 0;
            opacity: 0.9;
        }
        .login-body {
            padding: 40px 30px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #f1f3f4;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        .input-group {
            position: relative;
            margin-bottom: 20px;
        }
        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 10;
        }
        .input-group .form-control {
            padding-left: 45px;
        }
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }
        .demo-accounts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
        }
        .demo-accounts h6 {
            color: #495057;
            margin-bottom: 10px;
        }
        .demo-account {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 5px 0;
        }
        .demo-account:last-child {
            margin-bottom: 0;
        }
        .demo-account .username {
            font-weight: 600;
            color: #495057;
        }
        .demo-account .password {
            color: #6c757d;
            font-family: monospace;
        }
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .system-info {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="bi bi-shield-check" style="font-size: 3rem; margin-bottom: 15px;"></i>
            <h1>管理员控制台</h1>
            <p>企业级文件共享系统</p>
        </div>
        
        <div class="login-body">
            <div id="alert-container"></div>
            
            <form id="login-form">
                <div class="input-group">
                    <i class="bi bi-person"></i>
                    <input type="text" class="form-control" id="username" placeholder="用户名" required>
                </div>
                
                <div class="input-group">
                    <i class="bi bi-lock"></i>
                    <input type="password" class="form-control" id="password" placeholder="密码" required>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember-me">
                        <label class="form-check-label" for="remember-me">
                            记住我
                        </label>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-login" id="login-btn">
                    <span class="loading-spinner" id="loading-spinner"></span>
                    <span id="login-text">登录</span>
                </button>
            </form>
            
            <div class="demo-accounts">
                <h6><i class="bi bi-info-circle"></i> 演示账户</h6>
                <div class="demo-account">
                    <span class="username">admin</span>
                    <span class="password">admin123</span>
                </div>
                <div class="demo-account">
                    <span class="username">demo_user</span>
                    <span class="password">demo123</span>
                </div>
                <div class="demo-account">
                    <span class="username">test_user</span>
                    <span class="password">test123</span>
                </div>
            </div>
            
            <div class="system-info">
                <div>系统版本: v1.0.0</div>
                <div>服务状态: <span id="server-status" class="text-success">正常</span></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查服务器状态
            checkServerStatus();
            
            // 绑定表单提交事件
            document.getElementById('login-form').addEventListener('submit', handleLogin);
            
            // 绑定回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleLogin(e);
                }
            });
            
            // 自动填充演示账户（点击演示账户时）
            document.querySelectorAll('.demo-account').forEach(account => {
                account.addEventListener('click', function() {
                    const username = this.querySelector('.username').textContent;
                    const password = this.querySelector('.password').textContent;
                    
                    document.getElementById('username').value = username;
                    document.getElementById('password').value = password;
                });
            });
        });

        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    document.getElementById('server-status').textContent = '正常';
                    document.getElementById('server-status').className = 'text-success';
                } else {
                    document.getElementById('server-status').textContent = '异常';
                    document.getElementById('server-status').className = 'text-danger';
                }
            } catch (error) {
                document.getElementById('server-status').textContent = '离线';
                document.getElementById('server-status').className = 'text-danger';
            }
        }

        // 处理登录
        async function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember-me').checked;
            
            // 验证输入
            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }
            
            // 显示加载状态
            setLoading(true);
            
            try {
                // 调用登录API
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        user_type: 'admin',
                        remember_me: rememberMe
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    // 登录成功
                    showAlert('登录成功，正在跳转...', 'success');
                    
                    // 保存令牌
                    if (rememberMe) {
                        localStorage.setItem('admin_token', data.access_token);
                    } else {
                        sessionStorage.setItem('admin_token', data.access_token);
                    }
                    
                    // 跳转到管理界面
                    setTimeout(() => {
                        window.location.href = '/admin';
                    }, 1000);
                    
                } else {
                    // 登录失败
                    showAlert(data.message || '登录失败，请检查用户名和密码', 'danger');
                }
                
            } catch (error) {
                console.error('登录错误:', error);
                showAlert('网络错误，请稍后重试', 'danger');
            } finally {
                setLoading(false);
            }
        }

        // 设置加载状态
        function setLoading(loading) {
            const loginBtn = document.getElementById('login-btn');
            const loadingSpinner = document.getElementById('loading-spinner');
            const loginText = document.getElementById('login-text');
            
            if (loading) {
                loginBtn.disabled = true;
                loadingSpinner.style.display = 'inline-block';
                loginText.textContent = '登录中...';
            } else {
                loginBtn.disabled = false;
                loadingSpinner.style.display = 'none';
                loginText.textContent = '登录';
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alert-container');
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // 清除之前的提示
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.classList.remove('show');
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.parentNode.removeChild(alertDiv);
                        }
                    }, 150);
                }
            }, 3000);
        }

        // 检查是否已登录
        function checkLoginStatus() {
            const token = localStorage.getItem('admin_token') || sessionStorage.getItem('admin_token');
            if (token) {
                // 验证令牌有效性
                fetch('/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // 已登录，跳转到管理界面
                        window.location.href = '/admin';
                    }
                })
                .catch(error => {
                    // 令牌无效，清除存储
                    localStorage.removeItem('admin_token');
                    sessionStorage.removeItem('admin_token');
                });
            }
        }

        // 页面加载时检查登录状态
        checkLoginStatus();
    </script>
</body>
</html>
