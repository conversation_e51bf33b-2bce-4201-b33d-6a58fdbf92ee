"""
文件模型
"""
from sqlalchemy import Column, Integer, String, BigInteger, Boolean, DateTime, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from ..database import Base


class File(Base):
    """文件模型"""
    __tablename__ = "files"
    
    # 基础字段
    id = Column(Integer, primary_key=True, index=True, comment="文件ID")
    file_path = Column(String(1000), nullable=False, comment="文件完整路径")
    file_name = Column(String(255), nullable=False, index=True, comment="文件名")
    file_size = Column(BigInteger, nullable=False, comment="文件大小(字节)")
    file_type = Column(String(50), index=True, comment="文件类型")
    mime_type = Column(String(100), comment="MIME类型")
    file_hash = Column(String(64), index=True, comment="SHA256哈希")
    
    # 缩略图和预览
    thumbnail_path = Column(String(500), comment="缩略图路径")
    is_image = Column(Boolean, default=False, index=True, comment="是否为图片")
    is_sensitive = Column(Boolean, default=False, index=True, comment="是否为敏感文件")
    
    # 文件夹信息
    folder_path = Column(String(1000), nullable=False, index=True, comment="所在文件夹路径")
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_accessed = Column(DateTime(timezone=True), comment="最后访问时间")
    
    # 统计字段
    access_count = Column(Integer, default=0, comment="访问次数")
    
    # 关系
    download_records = relationship("DownloadRecord", back_populates="file")
    
    def __repr__(self):
        return f"<File(id={self.id}, name='{self.file_name}', size={self.file_size})>"
    
    @property
    def size_mb(self) -> float:
        """文件大小(MB)"""
        return self.file_size / (1024 * 1024)
    
    @property
    def extension(self) -> str:
        """文件扩展名"""
        import os
        return os.path.splitext(self.file_name)[1].lower()
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "file_path": self.file_path,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "size_mb": self.size_mb,
            "file_type": self.file_type,
            "mime_type": self.mime_type,
            "file_hash": self.file_hash,
            "thumbnail_path": self.thumbnail_path,
            "is_image": self.is_image,
            "is_sensitive": self.is_sensitive,
            "folder_path": self.folder_path,
            "extension": self.extension,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "access_count": self.access_count,
        }
