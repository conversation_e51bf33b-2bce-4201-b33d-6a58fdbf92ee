"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ScissorOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ScissorOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ScissorOutlined = function ScissorOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ScissorOutlined.default
  }));
};

/**![scissor](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU2Ny4xIDUxMmwzMTguNS0zMTkuM2M1LTUgMS41LTEzLjctNS42LTEzLjdoLTkwLjVjLTIuMSAwLTQuMi44LTUuNiAyLjNsLTI3My4zIDI3NC05MC4yLTkwLjVjMTIuNS0yMi4xIDE5LjctNDcuNiAxOS43LTc0LjggMC04My45LTY4LjEtMTUyLTE1Mi0xNTJzLTE1MiA2OC4xLTE1MiAxNTIgNjguMSAxNTIgMTUyIDE1MmMyNy43IDAgNTMuNi03LjQgNzUuOS0yMC4zbDkwIDkwLjMtOTAuMSA5MC4zQTE1MS4wNCAxNTEuMDQgMCAwMDI4OCA1ODJjLTgzLjkgMC0xNTIgNjguMS0xNTIgMTUyczY4LjEgMTUyIDE1MiAxNTIgMTUyLTY4LjEgMTUyLTE1MmMwLTI3LjItNy4yLTUyLjctMTkuNy03NC44bDkwLjItOTAuNSAyNzMuMyAyNzRjMS41IDEuNSAzLjUgMi4zIDUuNiAyLjNIODgwYzcuMSAwIDEwLjctOC42IDUuNi0xMy43TDU2Ny4xIDUxMnpNMjg4IDM3MGMtNDQuMSAwLTgwLTM1LjktODAtODBzMzUuOS04MCA4MC04MCA4MCAzNS45IDgwIDgwLTM1LjkgODAtODAgODB6bTAgNDQ0Yy00NC4xIDAtODAtMzUuOS04MC04MHMzNS45LTgwIDgwLTgwIDgwIDM1LjkgODAgODAtMzUuOSA4MC04MCA4MHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(ScissorOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ScissorOutlined';
}
var _default = exports.default = RefIcon;