<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 企业级文件共享系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-locked { background: #fff3cd; color: #856404; }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .search-box {
            border-radius: 25px;
            border: 1px solid #ddd;
            padding: 10px 20px;
        }
        .action-btn {
            padding: 5px 10px;
            margin: 2px;
            border-radius: 5px;
            border: none;
            font-size: 12px;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-people"></i> 用户管理</h2>
            <div>
                <button class="btn btn-success me-2" onclick="showCreateUserModal()">
                    <i class="bi bi-person-plus"></i> 新建用户
                </button>
                <button class="btn btn-primary" onclick="refreshUsers()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 搜索和过滤 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control search-box" id="search-input" 
                               placeholder="搜索用户名、邮箱或姓名..." onkeyup="searchUsers()">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="group-filter" onchange="filterUsers()">
                            <option value="">所有用户组</option>
                            <option value="admin">管理员</option>
                            <option value="upload">上传用户</option>
                            <option value="download">下载用户</option>
                            <option value="readonly">只读用户</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="status-filter" onchange="filterUsers()">
                            <option value="">所有状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">禁用</option>
                            <option value="locked">锁定</option>
                        </select>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="text-muted">共 <span id="total-users">0</span> 个用户</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list"></i> 用户列表</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>用户</th>
                                <th>邮箱</th>
                                <th>用户组</th>
                                <th>状态</th>
                                <th>下载配额</th>
                                <th>最后登录</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="users-table-body">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="bi bi-arrow-clockwise spin"></i> 加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <nav class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>

    <!-- 创建用户模态框 -->
    <div class="modal fade" id="createUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-person-plus"></i> 创建新用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="create-user-form">
                        <div class="mb-3">
                            <label class="form-label">用户名 *</label>
                            <input type="text" class="form-control" id="new-username" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码 *</label>
                            <input type="password" class="form-control" id="new-password" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="new-email">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-control" id="new-fullname">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">用户组</label>
                            <select class="form-select" id="new-usergroup">
                                <option value="readonly">只读用户</option>
                                <option value="download">下载用户</option>
                                <option value="upload">上传用户</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">下载配额 (MB)</label>
                            <input type="number" class="form-control" id="new-quota" value="500">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createUser()">创建用户</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-person-gear"></i> 编辑用户</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-user-form">
                        <input type="hidden" id="edit-user-id">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" id="edit-username" readonly>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" id="edit-email">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-control" id="edit-fullname">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">用户组</label>
                            <select class="form-select" id="edit-usergroup">
                                <option value="readonly">只读用户</option>
                                <option value="download">下载用户</option>
                                <option value="upload">上传用户</option>
                                <option value="admin">管理员</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">下载配额 (MB)</label>
                            <input type="number" class="form-control" id="edit-quota">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit-active">
                                <label class="form-check-label" for="edit-active">
                                    账户激活
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 20;
        let totalUsers = 0;
        let allUsers = [];
        let filteredUsers = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
        });

        // 加载用户数据
        async function loadUsers() {
            try {
                // 这里使用模拟数据，实际应该调用API
                const mockUsers = [
                    {
                        id: 1,
                        username: 'admin',
                        email: '<EMAIL>',
                        full_name: '系统管理员',
                        user_group: 'admin',
                        is_active: true,
                        is_locked: false,
                        download_quota: 10000,
                        remaining_quota: 9500,
                        created_at: '2024-01-01T00:00:00',
                        last_login: '2024-01-15T14:30:00'
                    },
                    {
                        id: 2,
                        username: 'demo_user',
                        email: '<EMAIL>',
                        full_name: '演示用户',
                        user_group: 'download',
                        is_active: true,
                        is_locked: false,
                        download_quota: 1000,
                        remaining_quota: 750,
                        created_at: '2024-01-02T00:00:00',
                        last_login: '2024-01-15T13:20:00'
                    },
                    {
                        id: 3,
                        username: 'test_user',
                        email: '<EMAIL>',
                        full_name: '测试用户',
                        user_group: 'upload',
                        is_active: true,
                        is_locked: false,
                        download_quota: 2000,
                        remaining_quota: 1800,
                        created_at: '2024-01-03T00:00:00',
                        last_login: '2024-01-15T12:10:00'
                    }
                ];

                allUsers = mockUsers;
                filteredUsers = [...allUsers];
                totalUsers = allUsers.length;
                
                updateUsersTable();
                updatePagination();
                document.getElementById('total-users').textContent = totalUsers;

            } catch (error) {
                console.error('加载用户数据失败:', error);
                showAlert('加载用户数据失败', 'danger');
            }
        }

        // 更新用户表格
        function updateUsersTable() {
            const tbody = document.getElementById('users-table-body');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageUsers = filteredUsers.slice(startIndex, endIndex);

            if (pageUsers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center py-4 text-muted">
                            <i class="bi bi-inbox"></i> 没有找到用户
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = pageUsers.map(user => `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="user-avatar me-3">
                                ${user.username.charAt(0).toUpperCase()}
                            </div>
                            <div>
                                <div class="fw-bold">${user.username}</div>
                                <small class="text-muted">${user.full_name || '-'}</small>
                            </div>
                        </div>
                    </td>
                    <td>${user.email || '-'}</td>
                    <td>
                        <span class="badge bg-${getUserGroupColor(user.user_group)}">
                            ${getUserGroupName(user.user_group)}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${getUserStatusClass(user)}">
                            ${getUserStatusText(user)}
                        </span>
                    </td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar" style="width: ${(user.remaining_quota / user.download_quota) * 100}%">
                                ${user.remaining_quota}/${user.download_quota}MB
                            </div>
                        </div>
                    </td>
                    <td>${formatDateTime(user.last_login)}</td>
                    <td>${formatDateTime(user.created_at)}</td>
                    <td>
                        <button class="action-btn btn btn-sm btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="action-btn btn btn-sm btn-outline-warning" onclick="resetPassword(${user.id})" title="重置密码">
                            <i class="bi bi-key"></i>
                        </button>
                        <button class="action-btn btn btn-sm btn-outline-${user.is_locked ? 'success' : 'warning'}" 
                                onclick="toggleLock(${user.id})" title="${user.is_locked ? '解锁' : '锁定'}">
                            <i class="bi bi-${user.is_locked ? 'unlock' : 'lock'}"></i>
                        </button>
                        ${user.username !== 'admin' ? `
                        <button class="action-btn btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                        ` : ''}
                    </td>
                </tr>
            `).join('');
        }

        // 辅助函数
        function getUserGroupColor(group) {
            const colors = {
                'admin': 'danger',
                'upload': 'success',
                'download': 'primary',
                'readonly': 'secondary'
            };
            return colors[group] || 'secondary';
        }

        function getUserGroupName(group) {
            const names = {
                'admin': '管理员',
                'upload': '上传用户',
                'download': '下载用户',
                'readonly': '只读用户'
            };
            return names[group] || group;
        }

        function getUserStatusClass(user) {
            if (user.is_locked) return 'status-locked';
            return user.is_active ? 'status-active' : 'status-inactive';
        }

        function getUserStatusText(user) {
            if (user.is_locked) return '锁定';
            return user.is_active ? '活跃' : '禁用';
        }

        function formatDateTime(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN');
        }

        // 搜索用户
        function searchUsers() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            filteredUsers = allUsers.filter(user => 
                user.username.toLowerCase().includes(searchTerm) ||
                (user.email && user.email.toLowerCase().includes(searchTerm)) ||
                (user.full_name && user.full_name.toLowerCase().includes(searchTerm))
            );
            
            currentPage = 1;
            updateUsersTable();
            updatePagination();
        }

        // 过滤用户
        function filterUsers() {
            const groupFilter = document.getElementById('group-filter').value;
            const statusFilter = document.getElementById('status-filter').value;
            
            filteredUsers = allUsers.filter(user => {
                let matchGroup = !groupFilter || user.user_group === groupFilter;
                let matchStatus = true;
                
                if (statusFilter === 'active') {
                    matchStatus = user.is_active && !user.is_locked;
                } else if (statusFilter === 'inactive') {
                    matchStatus = !user.is_active;
                } else if (statusFilter === 'locked') {
                    matchStatus = user.is_locked;
                }
                
                return matchGroup && matchStatus;
            });
            
            currentPage = 1;
            updateUsersTable();
            updatePagination();
        }

        // 更新分页
        function updatePagination() {
            const totalPages = Math.ceil(filteredUsers.length / pageSize);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let paginationHTML = '';
            
            // 上一页
            paginationHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
                </li>
            `;
            
            // 页码
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
                    paginationHTML += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 2 || i === currentPage + 2) {
                    paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }
            
            // 下一页
            paginationHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
                </li>
            `;
            
            pagination.innerHTML = paginationHTML;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(filteredUsers.length / pageSize);
            if (page < 1 || page > totalPages) return;
            
            currentPage = page;
            updateUsersTable();
            updatePagination();
        }

        // 显示创建用户模态框
        function showCreateUserModal() {
            const modal = new bootstrap.Modal(document.getElementById('createUserModal'));
            modal.show();
        }

        // 创建用户
        function createUser() {
            const userData = {
                username: document.getElementById('new-username').value,
                password: document.getElementById('new-password').value,
                email: document.getElementById('new-email').value,
                full_name: document.getElementById('new-fullname').value,
                user_group: document.getElementById('new-usergroup').value,
                download_quota: parseInt(document.getElementById('new-quota').value)
            };
            
            // 验证数据
            if (!userData.username || !userData.password) {
                showAlert('用户名和密码不能为空', 'danger');
                return;
            }
            
            // 这里应该调用API创建用户
            console.log('创建用户:', userData);
            showAlert('用户创建成功', 'success');
            
            // 关闭模态框并刷新列表
            bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide();
            loadUsers();
        }

        // 编辑用户
        function editUser(userId) {
            const user = allUsers.find(u => u.id === userId);
            if (!user) return;
            
            // 填充表单
            document.getElementById('edit-user-id').value = user.id;
            document.getElementById('edit-username').value = user.username;
            document.getElementById('edit-email').value = user.email || '';
            document.getElementById('edit-fullname').value = user.full_name || '';
            document.getElementById('edit-usergroup').value = user.user_group;
            document.getElementById('edit-quota').value = user.download_quota;
            document.getElementById('edit-active').checked = user.is_active;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        }

        // 更新用户
        function updateUser() {
            const userId = document.getElementById('edit-user-id').value;
            const userData = {
                email: document.getElementById('edit-email').value,
                full_name: document.getElementById('edit-fullname').value,
                user_group: document.getElementById('edit-usergroup').value,
                download_quota: parseInt(document.getElementById('edit-quota').value),
                is_active: document.getElementById('edit-active').checked
            };
            
            // 这里应该调用API更新用户
            console.log('更新用户:', userId, userData);
            showAlert('用户更新成功', 'success');
            
            // 关闭模态框并刷新列表
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadUsers();
        }

        // 重置密码
        function resetPassword(userId) {
            if (confirm('确定要重置该用户的密码吗？')) {
                // 这里应该调用API重置密码
                console.log('重置密码:', userId);
                showAlert('密码重置成功，新密码已发送到用户邮箱', 'success');
            }
        }

        // 切换锁定状态
        function toggleLock(userId) {
            const user = allUsers.find(u => u.id === userId);
            if (!user) return;
            
            const action = user.is_locked ? '解锁' : '锁定';
            if (confirm(`确定要${action}该用户吗？`)) {
                // 这里应该调用API切换锁定状态
                console.log('切换锁定状态:', userId);
                showAlert(`用户${action}成功`, 'success');
                loadUsers();
            }
        }

        // 删除用户
        function deleteUser(userId) {
            if (confirm('确定要删除该用户吗？此操作不可恢复！')) {
                // 这里应该调用API删除用户
                console.log('删除用户:', userId);
                showAlert('用户删除成功', 'success');
                loadUsers();
            }
        }

        // 刷新用户列表
        function refreshUsers() {
            loadUsers();
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }

        // CSS动画
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
